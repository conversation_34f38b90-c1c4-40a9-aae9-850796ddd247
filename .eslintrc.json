{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}, "import/resolver": {"node": {"extensions": [".js", ".jsx", ".d.ts", ".ts", ".tsx"], "moduleDirectory": ["node_modules", "src"]}}}, "extends": ["plugin:prettier/recommended", "plugin:import/warnings", "plugin:import/typescript"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "prettier/prettier": ["error", {"endOfLine": "auto"}], "import/order": ["error", {"groups": ["builtin", "external", "internal"], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}], "pathGroupsExcludedImportTypes": ["react"], "alphabetize": {"order": "asc", "caseInsensitive": true}}]}, "overrides": [{"files": ["*.scss"], "rules": {"no-unused-vars": "off", "no-undef": "off"}}]}