{
  "$schema": "https://json.schemastore.org/tsconfig",
  "_version": "2.0.0",

  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "moduleResolution": "node", // Change this to 'node' or 'node16'
    "target": "es2015",
    "allowJs": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "noEmit": true,
    "noFallthroughCasesInSwitch": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "paths": {
      "@/*": ["./src/*"]
    }
  },

  "include": ["src/**/*", "src/globals.d.ts"],
  "exclude": ["node_modules", "build"]
}
