{"name": "go-manage-bo", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@craco/craco": "^7.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.1", "@mui/styled-engine-sc": "^6.2.0", "@mui/system": "^7.0.2", "@mui/x-date-pickers": "^7.23.2", "@mui/x-date-pickers-pro": "^7.25.0", "@reduxjs/toolkit": "^2.5.1", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@syncfusion/ej2-base": "^28.1.33", "@syncfusion/ej2-react-schedule": "^28.1.39", "@tanstack/react-table": "^8.21.3", "apexcharts": "^4.3.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "cra-template-typescript": "1.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "husky": "^9.1.7", "jspdf": "^3.0.1", "lodash": "^4.17.21", "query-string": "^9.1.1", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-color": "^2.19.3", "react-custom-scrollbars-2": "^4.5.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-movable": "^3.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-scripts": "5.0.1", "react-window": "^1.8.11", "remixicon": "^4.5.0", "sonner": "^1.7.1", "styled-components": "^6.1.13", "survey-core": "^1.12.20", "survey-creator-core": "^1.12.20", "survey-creator-react": "^1.12.20", "survey-react-ui": "^1.12.17", "swiper": "^11.1.15", "swr": "^2.2.5", "typescript": "^5.7.2", "universal-cookie": "^7.2.2", "uuid": "^11.1.0", "yup": "^1.6.1"}, "scripts": {"start": "craco start", "runIOS": "NODE_OPTIONS='--max-old-space-size=4096' craco start", "prebuild": "node scripts/select-env.js", "build": "craco --max_old_space_size=4096 build", "build-staging-window": "set APP_ENV=staging && npm run build", "build-staging-linux": "export APP_ENV=staging && npm run build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,md}": "eslint --fix", "**/*.scss": "echo \"Skipping .scss files for linting\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/aws-sdk": "^2.7.4", "@types/lodash": "^4.17.14", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.0.2", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.4.2", "sass": "^1.83.0", "sass-loader": "^16.0.4"}}