import path from 'path'
import { Configuration } from 'webpack'

module.exports = {
  webpack: {
    configure: (webpackConfig: Configuration, { env, paths }: { env: string; paths: any }) => {
      // Ensure webpackConfig.resolve is defined
      webpackConfig.resolve = webpackConfig.resolve || {}

      // Add custom aliases
      webpackConfig.resolve.alias = {
        ...(webpackConfig.resolve.alias || {}),
        '@': path.resolve(__dirname, 'src'),
      }

      return webpackConfig
    },
  },
}
