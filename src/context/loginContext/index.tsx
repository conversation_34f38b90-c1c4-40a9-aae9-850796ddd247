// import React, { useEffect, useReducer, useContext } from 'react'

// import { useAuth0 } from '@auth0/auth0-react'
// import { ILoginContext, InitialLoginState } from './Interface'
// import loginReducer from './LoginReducer'

// const initialState: InitialLoginState = {
//   userDetail: null,
// }

// const LoginContext = React.createContext<ILoginContext>({ state: initialState, dispatch: () => null })

// const LoginProvider: React.FC<{ children: any }> = ({ children }) => {
//   const [state, dispatch] = useReducer(loginReducer, initialState)
//   const { isLoading, isAuthenticated, getIdTokenClaims, user, loginWithRedirect, logout } = useAuth0()
//   const clientId = process.env.REACT_APP_CLIENT_ID || ''

//   const validateToken = async () => {
//     try {
//       if (isAuthenticated) {
//         const idTokenClaims = await getIdTokenClaims()
//         const expirationTime = idTokenClaims?.exp ? idTokenClaims.exp * 1000 : null
//         if (expirationTime && Date.now() > expirationTime) {
//           console.warn('Token expired. Logging out...')
//           logout({
//             logoutParams: {
//               client_id: clientId,
//             },
//           })
//           await loginWithRedirect({ appState: { returnTo: '/' } })
//         }
//       } else {
//         await loginWithRedirect({ appState: { returnTo: '/' } })
//       }
//     } catch (error) {
//       console.error('Error validating token:', error)
//       await loginWithRedirect({ appState: { returnTo: '/' } })
//     }
//   }

//   useEffect(() => {
//     if (!isLoading) {
//       validateToken()
//     }
//   }, [isAuthenticated, isLoading])

//   return <LoginContext.Provider value={{ state, dispatch }}>{children}</LoginContext.Provider>
// }

// const useLogin = () => {
//   const context = useContext(LoginContext)
//   if (context === undefined) {
//     throw new Error('useLogin must be used within a LoginProvider')
//   }
//   return context
// }

// export { LoginProvider, useLogin }

import React, { useEffect, useReducer, useContext, useState } from 'react'
import { useAuth0 } from '@auth0/auth0-react'
import { ILoginContext, InitialLoginState } from './Interface'
import loginReducer from './LoginReducer'

const initialState: InitialLoginState = {
  userDetail: null,
}

const LoginContext = React.createContext<ILoginContext>({
  state: initialState,
  dispatch: () => null,
})

const LoginProvider: React.FC<{ children: any }> = ({ children }) => {
  const [state, dispatch] = useReducer(loginReducer, initialState)
  const { isLoading, isAuthenticated, getIdTokenClaims, loginWithRedirect, logout } = useAuth0()
  const clientId = process.env.REACT_APP_CLIENT_ID || ''
  const [authChecked, setAuthChecked] = useState(false)

  useEffect(() => {
    if (isLoading || authChecked) return
    ;(async function validateToken() {
      try {
        if (isAuthenticated) {
          const claims = await getIdTokenClaims()
          const expMs = claims?.exp ? claims.exp * 1000 : 0

          if (Date.now() > expMs) {
            console.warn('Token expired — clearing caches and reloading')
            // Log out user
            await logout({ logoutParams: { client_id: clientId } })

            // Clear Service Worker caches (if any)
            if ('caches' in window) {
              const cacheNames = await caches.keys()
              await Promise.all(cacheNames.map((name) => caches.delete(name)))
            }

            // Force full reload
            window.location.replace(window.location.href)
          } else {
            // Check if this is the first time after login (no previous auth state)
            const hasReloadedAfterLogin = sessionStorage.getItem('hasReloadedAfterLogin')

            if (!hasReloadedAfterLogin) {
              sessionStorage.setItem('hasReloadedAfterLogin', 'true')
              setAuthChecked(true)
              return
            }

            setAuthChecked(true)
          }
        } else {
          console.log('2')
          // Clear the reload flag when not authenticated
          sessionStorage.removeItem('hasReloadedAfterLogin')
          await loginWithRedirect({ appState: { returnTo: '/' } })
        }
      } catch (err) {
        console.log('3')
        console.error('Validation error:', err)
        await loginWithRedirect({ appState: { returnTo: '/' } })
      }
    })()
  }, [isLoading, isAuthenticated, authChecked, getIdTokenClaims, loginWithRedirect, logout, clientId])

  if (!authChecked) return null

  return <LoginContext.Provider value={{ state, dispatch }}>{children}</LoginContext.Provider>
}

const useLogin = () => {
  const context = useContext(LoginContext)
  if (!context) {
    throw new Error('useLogin must be used within a LoginProvider')
  }
  return context
}

export { LoginProvider, useLogin }
