import { useCallback, useEffect, useRef, useState } from 'react'

interface WebSocketOptions {
  url: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  onMessage?: (data: any) => void
  isEnabled?: boolean // Flag to control whether the WebSocket should connect
}

/**
 * A simplified hook for WebSocket connections
 * - Handles connection, reconnection, and message parsing
 * - Provides connection status and message sending capability
 * - Automatically reconnects on disconnection
 * - Supports conditional connection based on isEnabled flag
 * - Cleans up resources when disabled or unmounted
 */
export function useWebSocket(options: WebSocketOptions) {
  const { url, reconnectInterval = 3000, maxReconnectAttempts = 5, onMessage, isEnabled = true } = options

  const [isConnected, setIsConnected] = useState(false)
  const socketRef = useRef<WebSocket | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Handle incoming messages with robust error handling
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        // Safely parse incoming JSON messages
        const data = JSON.parse(event.data)
        onMessage?.(data)
      } catch (error) {
        // Log parsing errors without breaking the hook
        console.error('Error parsing WebSocket message:', error, '\nRaw data:', event.data)
        // Still call onMessage with the raw data to allow custom handling
        onMessage?.(event.data)
      }
    },
    [onMessage],
  )

  // Connect to WebSocket
  const connect = useCallback(() => {
    // Don't reconnect if already connected or connecting
    if (
      socketRef.current &&
      (socketRef.current.readyState === WebSocket.CONNECTING || socketRef.current.readyState === WebSocket.OPEN)
    ) {
      return
    }

    try {
      // Create new WebSocket connection
      const socket = new WebSocket(url)

      // Set up event handlers
      socket.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        reconnectAttemptsRef.current = 0
      }

      socket.onmessage = handleMessage

      socket.onclose = (event) => {
        console.log(`WebSocket disconnected: ${event.code} ${event.reason}`)
        setIsConnected(false)
        socketRef.current = null

        // Only attempt to reconnect if not a normal closure
        if (event.code !== 1000) {
          handleReconnect()
        }
      }

      socket.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

      socketRef.current = socket
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error)
      handleReconnect()
    }
  }, [url, handleMessage])

  // Handle reconnection logic
  const handleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.log('Maximum reconnection attempts reached')
      return
    }

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current)
    }

    reconnectAttemptsRef.current += 1
    console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`)

    reconnectTimerRef.current = setTimeout(() => {
      connect()
    }, reconnectInterval)
  }, [connect, maxReconnectAttempts, reconnectInterval])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current)
      reconnectTimerRef.current = null
    }

    if (socketRef.current) {
      socketRef.current.close()
      socketRef.current = null
      setIsConnected(false)
    }
  }, [])

  // Send message through WebSocket
  const send = useCallback((data: string | object) => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error('Cannot send message: WebSocket is not connected')
      return false
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      socketRef.current.send(message)
      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      return false
    }
  }, [])

  // Connect only when enabled, disconnect when disabled or unmounting
  useEffect(() => {
    if (isEnabled) {
      connect()
    } else {
      disconnect()
    }
    return () => disconnect()
  }, [connect, disconnect, isEnabled])

  return {
    isConnected,
    send,
    connect,
    disconnect,
  }
}
