import { useCallback, useState } from 'react'
import { AxiosError, AxiosPromise, AxiosRequestConfig, AxiosResponse } from 'axios'

export type AxiosApi<ResponseData, Payload> = (
  payload?: Payload,
  config?: AxiosRequestConfig,
) => AxiosPromise<ResponseData>

export default function useAxiosApi<ResponseData = unknown, Payload = unknown, ErrorData = unknown>(
  api: AxiosApi<ResponseData, Payload>,
  options?: {
    onFulfilled?: (response: AxiosResponse<ResponseData>) => void
    onRejected?: (error: AxiosError<ErrorData>) => void
    onStart?: () => void
    onFinish?: () => void
  },
) {
  const [isLoading, setIsLoading] = useState(false)
  const [isError, setIsError] = useState(false)
  const [data, setData] = useState<ResponseData>()
  const [response, setResponse] = useState<AxiosResponse<ResponseData>>()
  const [error, setError] = useState<AxiosError<ErrorData>>()

  const execute = useCallback(
    async (payload?: Payload, config?: AxiosRequestConfig) => {
      options?.onStart?.()
      setIsLoading(true)
      setIsError(false)
      setData(undefined)
      setResponse(undefined)
      setError(undefined)

      try {
        const resp = await api(payload, config)
        setIsLoading(false)
        setData(resp.data)
        setResponse(resp)
        options?.onFulfilled?.(resp)
        return resp
      } catch (err: unknown) {
        const axiosError = err as AxiosError<ErrorData>
        setIsLoading(false)
        setIsError(true)
        setError(axiosError)
        options?.onRejected?.(axiosError)
        throw axiosError
      } finally {
        options?.onFinish?.()
      }
    },
    [api, options],
  )

  return {
    execute,
    isLoading,
    isError,
    data,
    setData,
    response,
    error,
  }
}
