import makeGetHook from '../makeGetHook/makeGetHook'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUserDetails {
  userGMID: number
  givenName: string
  familyName: string
  email: string
  phoneLandline: string | null
  phoneMobile: string | null
  createDate: string | null
  isApp: boolean | null
  address: string
  dateOfBirth: string // ISO 8601 date string
  gender: 'Male' | 'Female' | 'Other' // Adjust if more options are needed
  outstandingBalance: number
  lastVisit: string | null // ISO 8601 date string or null
  isActive: boolean
  bookingType: string | null
  staffMember: string | null
  bookingTime: string | null // ISO 8601 datetime string or null
  bookingSource: string | null
  bookingService: string | null
  paymentStatus: string | null
  rating: string | null
  client_sales: string | null
}

const useGetAllUSers = makeGetHook<IUserDetails[]>(USER.GET_USERS, fetcher)

const useGetUserById = (id: number): { data: IUserDetails | undefined } => {
  const { data } = useGetAllUSers()
  const filteredData = data?.find((item) => item.userGMID === id)

  return { data: filteredData }
}

export { useGetAllUSers, useGetUserById }
