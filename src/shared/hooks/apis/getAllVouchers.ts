import makeGetHook from '../makeGetHook/makeGetHook'
import { VOUCHER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucher {
  clientEmail: string
  clientFirstName: string
  clientSurname: string
  dateCreated: string
  description: string
  emailTo: string
  expire: string
  initialBalance: number
  isUsed: boolean
  remainingBalance: number
  uniqueVoucherCode: number
  voucherId: number
}
const useGetAllVouchers = makeGetHook<IVoucher>(VOUCHER.GET_ALL_VOUCHERS, fetcher)

export { useGetAllVouchers }
