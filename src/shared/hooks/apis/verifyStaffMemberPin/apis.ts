import { AxiosRequestConfig } from 'axios'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVerifyStaffMemberPinPayload {
  id: string
  pin: string
}

export default {
  verifyStaffMemberPin: (
    payload?: IVerifyStaffMemberPinPayload,
    config?: AxiosRequestConfig<IVerifyStaffMemberPinPayload>,
  ) => fetcher.post(STAFF.VERIFY_STAFF_MEMBER_PIN, payload, config),
}
