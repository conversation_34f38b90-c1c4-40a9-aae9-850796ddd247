import makeGetHook from '../../makeGetHook/makeGetHook'
import { PERMISSIONS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IPagePermissions {
  appointments: boolean
  staff: boolean
  staff_services: boolean
  products: boolean
  promo_codes: boolean
  clients: boolean
  sales_manager: boolean
  make_a_sale: boolean
  marketing: boolean
  reports: boolean
  settings: boolean
}

export interface IRolePermissions {
  permissions: {
    pages: IPagePermissions
  }
}

export type IPermissionsResponse = Record<string, IRolePermissions>

export const useGetPermissions = makeGetHook<IPermissionsResponse>(PERMISSIONS.GET_PERMISSIONS, fetcher)
