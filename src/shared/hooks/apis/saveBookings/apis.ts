import { AxiosRequestConfig } from 'axios'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ISaveBookingsPayload {
  employeeId: string
  clientId: number
  description: string
  startTime: string
  endTime: string
  isApp: boolean
  id: number
  isAllDay: boolean | null
  customer: null
  service: string
  serviceId: number
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
}

export default {
  saveBookings: (payload?: ISaveBookingsPayload[], config?: AxiosRequestConfig<ISaveBookingsPayload[]>) =>
    fetcher.post(BOOKINGS.SAVE_BOOKINGS, payload, config),
}
