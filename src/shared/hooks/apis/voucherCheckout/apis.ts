import { AxiosRequestConfig } from 'axios'
import { VOUCHER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucherCheckoutPayload {
  voucherCodes: string[]
  groupAppointmentIds: string[]
  serviceAmount: number
}

export default {
  voucherCheckout: (payload?: IVoucherCheckoutPayload, config?: AxiosRequestConfig<IVoucherCheckoutPayload>) =>
    fetcher.post(VOUCHER.UPDATE_REDEEM_VOUCHER, payload, config),
}
