import makeGetHook from '../makeGetHook/makeGetHook'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IGetBusinessHours {
  day: string
  open_status: boolean
  start_time: string
  end_time: string
}

const useGetBusinessHours = makeGetHook<IGetBusinessHours[]>(STAFF.GET_BUSINESS_HOURS, fetcher)

const useGetBusinessHour = (currentDay: string) => {
  const { data, ...rest } = useGetBusinessHours()

  data?.push({ day: 'Monday', open_status: true, start_time: '09:00', end_time: '23:00' })
  const filteredData = data?.find((item) => item.day === currentDay)

  return { data: filteredData, ...rest }
}

export { useGetBusinessHours, useGetBusinessHour }
