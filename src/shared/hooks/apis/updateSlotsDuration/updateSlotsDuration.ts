import useAxiosApi from '../../useAxiosApi'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUpdateSlotsDurationPayload {
  duration: string
}

export default function useUpdateSlotsDuration<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, IUpdateSlotsDurationPayload>((payload) =>
    fetcher.post(SETTINGS.SET_SLOT_DURATION, payload),
  )
  return parameters
}
