import { AxiosRequestConfig } from 'axios'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAddFaqPayload {
  title: string
  description: string
  status?: number
  priority?: number
}

export default {
  addFaq: (payload?: IAddFaqPayload, config?: AxiosRequestConfig<IAddFaqPayload>) =>
    fetcher.post(SETTINGS.ADD_NEW_FAQ, payload, config),
}
