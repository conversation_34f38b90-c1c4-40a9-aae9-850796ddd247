import makeGetHook from '../../makeGetHook/makeGetHook'
import useAxiosApi from '../../useAxiosApi'
import { CATALOG } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

interface IAllClientForm {
  clientId: string
  formDate: string
  formId: number
  id: number
  isFormComplete: number
}

const useGetAllClientForm = makeGetHook<IAllClientForm[]>(CATALOG.GET_ALL_CLIENT_FORM, fetcher)

const apis = {
  getClientFormById: (id: string) => fetcher.get(`${CATALOG.GET_ALL_CLIENT_FORM}?id=${id}`),
}

export default function useGetClientFormByIdAPI<ResponseData = unknown>(id: string) {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getClientFormById(id))
  return parameters
}

export { useGetAll<PERSON>lient<PERSON>orm, useGetClientFormByIdAPI }
