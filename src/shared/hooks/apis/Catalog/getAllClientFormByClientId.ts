import useAxiosApi from '../../useAxiosApi'
import { CATALOG } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAllClientForm {
  clientId: string
  formDate: string
  formId: number
  id: number
  isFormComplete: number
}

const apis = {
  getClientFormByClientId: (clientId: string) => fetcher.get(`${CATALOG.GET_ALL_CLIENT_FORM_BY_CLIENT_ID}/${clientId}`),
}

export default function useGetAllClientFormByClientId<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, {}>((clientId: any) => apis.getClientFormByClientId(clientId))
  return parameters
}

export { useGetAllClientFormByClientId }
