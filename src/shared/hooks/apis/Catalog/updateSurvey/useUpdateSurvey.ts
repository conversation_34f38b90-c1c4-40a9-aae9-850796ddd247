import useAxiosApi from '../../../useAxiosApi'
import { ICreateSurvey } from '../interface'
import apis from './apis'

export default function useUpdateSurvey<ResponseData = unknown>(selectedFormId: number | null) {
  const parameters = useAxiosApi<ResponseData, ICreateSurvey>((payload, config) => {
    if (selectedFormId === null) {
      return Promise.reject(console.log('Invalid form ID'))
    }
    return apis.updateSurvey(selectedFormId, payload, config)
  })

  return parameters
}
