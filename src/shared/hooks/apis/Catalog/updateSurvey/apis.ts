import { AxiosRequestConfig } from 'axios'
import { ICreateSurvey } from '../interface'
import { CATALOG } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export default {
  updateSurvey: (selectedFormId: number, payload?: ICreateSurvey, config?: AxiosRequestConfig<ICreateSurvey>) =>
    fetcher.put(`${CATALOG.UPDATE_CATALOG_FORM}/${selectedFormId}`, payload, config),
} as {
  updateSurvey: (
    selectedFormId: number,
    payload?: ICreateSurvey,
    config?: AxiosRequestConfig<ICreateSurvey>,
  ) => Promise<any>
}
