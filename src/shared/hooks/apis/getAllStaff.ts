import makeGetHook from '../makeGetHook/makeGetHook'
import useAxios<PERSON><PERSON> from '../useAxiosApi'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IShiftDetails {
  id: number
  employeeId: number
  startShiftTime: string // Format: HH:mm:ss
  endShiftTime: string // Format: HH:mm:ss
  startShiftTime12Hour: string | null // 12-hour format, or null if not provided
  endShiftTime12Hour: string | null // 12-hour format, or null if not provided
  outOfOfficeFrom: string // Format: HH:mm:ss
  outOfOfficeTo: string // Format: HH:mm:ss
  outOfOfficeFrom12Hour: string | null // 12-hour format, or null if not provided
  outOfOfficeTo12Hour: string | null // 12-hour format, or null if not provided
  timeAwayFrom: string | null // Format: HH:mm:ss, or null if not provided
  timeAwayTo: string | null // Format: HH:mm:ss, or null if not provided
  workDate: string // Format: YYYY-MM-DD
  dayId: number // 0-based index for the day
  description: string // Any additional description
  miscEventStartTime: string // Format: HH:mm:ss
  miscEventEndTime: string // Format: HH:mm:ss
  outsideBooking: boolean // Indicates if the booking is outside
}

export interface IStaffDetails {
  address: string // Employee's address
  commission: number // Commission amount (numeric, assumed zero if not specified)
  dateOfBirth: string // Format: YYYY-MM-DD
  email: string // Email address
  emergencyContactName: string // Name of the emergency contact
  emergencyContactNumber: string // Contact number of the emergency contact
  employeeBio: string // Short biography of the employee
  employeeImg: string // Base64-encoded image string
  employee_id: number // Unique employee ID
  firstName: string // Employee's first name
  gender: string // Gender (e.g., "Female")
  lastName: string // Employee's last name
  monthlySales: number // Monthly sales figure
  number: string // Contact number
  performance: string // Performance metric (e.g., "0")
  performedServices: number[] // List of service IDs performed by the employee
  pin: number // PIN for employee access
  password: number // Password for employee access
  reviews: string // Review count or score
  role: number // Role identifier (e.g., 1 for a specific role)
  staffDetailFormatted: IShiftDetails[]
}

const useGetAllStaff = makeGetHook<IStaffDetails[]>(STAFF.GET_ALL_STAFF, fetcher)

const useGetStaffById = (id: number): { data: IStaffDetails | undefined } => {
  const { data } = useGetAllStaff()

  const filteredData = data?.find((item) => item.employee_id === id)

  return { data: filteredData }
}

const apis = {
  getStaff: () => fetcher.get(`${STAFF.GET_ALL_STAFF}`),
}

export default function useGetStaffAPI<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getStaff())
  return parameters
}
const useGetStaff = makeGetHook<IStaffDetails>(STAFF.GET_ALL_STAFF, fetcher)

export { useGetStaffAPI, useGetAllStaff, useGetStaffById, useGetStaff }
