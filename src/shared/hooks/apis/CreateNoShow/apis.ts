import { AxiosRequestConfig } from 'axios'
import { STRIPE } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ICreateStripeChargePayload {
  email: string | undefined
  amount: number | undefined
  description: string | undefined // now matches your payload
  groupAppointmentIds: (string | undefined)[] | string
  customerId: string | undefined
  depositAmount: number | undefined
  paymentMethodId: string | undefined
}

export default {
  createnowShowStripeCharge: (
    payload?: ICreateStripeChargePayload,
    config?: AxiosRequestConfig<ICreateStripeChargePayload>,
  ) => fetcher.post(STRIPE.CREATE_NOSTRIPE_CHARGE, payload, config),
}
