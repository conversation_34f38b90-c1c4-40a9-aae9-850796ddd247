import { AxiosRequestConfig } from 'axios'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IValidateUserPayload {
  email: string
}

export default {
  validateUserExists: (payload?: IValidateUserPayload, config?: AxiosRequestConfig<IValidateUserPayload>) =>
    fetcher.get(`${USER.VALIDATE_USER_EXISTS}?email=${payload?.email}`, config),
}
