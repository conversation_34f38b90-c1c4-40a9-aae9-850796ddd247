import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IClientNotesPayload {
  id: number
  clientId: number
  date: string
  time: string
  comment: string
  status: boolean
  priorityNote: boolean
  isMobile: boolean
  isBo: boolean
  added_by: string | null
}

// GET Hook (to retrieve client notes)
const useGetClientNotes = makeGetHook<IClientNotesPayload[]>(USER.GET_CLIENT_NOTES, fetcher)

const apis = {
  getClientNotes: (clientId: string) => fetcher.get(`${USER.GET_CLIENT_NOTES}?clientId=${clientId}`),
}

// Custom hook for getting client notes
export default function useGetClientNotesAPI<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, {}>((clientId: any) => apis.getClientNotes(clientId))
  return parameters
}

export { useGetClientNotes, useGetClientNotesAPI }
