import { AxiosRequestConfig } from 'axios'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IBusinessSetting {
  setting_id: number
  logo: string
  site_name: string
  businessAddress: string
  businessWebsite: string
  companyTradingName: string
  tel: string
  default_email: string
  language: 'eng' | string
  businessOwnerVersion: string
  mobileAppVersion: string
  theme: string
  timezone: 'est' | string
  currency_prefix: string
  stripe: boolean
  smsEnabled: boolean
  smsClientName: string
  colour_primary: string
  colour_secondary: string
  colour_text: string
  bio: string
  is_deleted: boolean
  mobileAppUrl: string
  giftEnabled: boolean
}

export default {
  updateOwnerSettings: (payload?: IBusinessSetting, config?: AxiosRequestConfig<IBusinessSetting>) =>
    fetcher.post(SETTINGS.UPDATE_OWNER_SETTINGS, payload, config),
}
