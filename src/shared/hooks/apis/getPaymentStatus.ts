import { STRIPE } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

interface IPaymentStatusParams {
  paymentId: string
}

const useGetPaymentStatus = () => {
  const execute = async ({ paymentId }: IPaymentStatusParams) => {
    try {
      console.log('Calling getPaymentStatus with paymentId:', paymentId)
      const endpoint = `${STRIPE.GET_PAYMENT_STATUS}?paymentId=${paymentId}`
      console.log('Endpoint:', endpoint)
      const response = await fetcher.get(endpoint)
      console.log(response, 'response=====')
      return response.data
    } catch (error) {
      console.error('Error in getPaymentStatus:', error)
      throw error
    }
  }

  return { execute }
}

export { useGetPaymentStatus }
