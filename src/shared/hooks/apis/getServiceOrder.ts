import useAxiosApi from '../useAxiosApi'
import { SERVICES } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

const apis = {
  getServiceOrder: (serviceId: string, indexId: string, categoryId: string) =>
    fetcher.get(`${SERVICES.UPDATE_SERVICE_ORDER}?serviceId=${serviceId}&indexId=${indexId}&categoryId=${categoryId}`),
}

export default function useGetServiceOrder<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, {}>(({ serviceId, indexId, categoryId }: any) => {
    console.log(serviceId, serviceId, categoryId)

    return apis.getServiceOrder(serviceId, indexId, categoryId)
  })
  return parameters
}

export { useGetServiceOrder }
