import { AxiosRequestConfig } from 'axios'
import { VOUCHER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucherItem {
  clientEmail?: string
  clientFirstName?: string
  clientSurname?: string
  description: string
  emailTo?: string
  expire: string
  initialBalance: number
  remainingBalance: number
}

export type IAddVoucherPayload = IVoucherItem[]

export default {
  addVoucher: (payload?: IVoucherItem[], config?: AxiosRequestConfig<IVoucherItem[]>) =>
    fetcher.post(VOUCHER.ADD_VOUCHER, payload, config),
}
