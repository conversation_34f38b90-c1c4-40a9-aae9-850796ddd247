import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAppointment {
  allDay?: boolean
  cancelled?: boolean
  categoryId?: string
  categoryName?: string
  clientId?: number
  colourCode?: string
  customer?: string
  customerId?: string
  description?: string
  email?: string
  employeeId?: number
  employeeName?: string
  employeeImg?: string
  endTime?: string
  eventType?: string | null
  firstName?: string
  groupAppointmentId?: string
  hasEmail?: boolean
  id?: number
  isApp?: boolean
  lastName?: string
  newUser?: boolean
  outstandingBalance?: number
  paymentMethodId?: string
  paymentReceipt?: string | null
  pending?: boolean
  pendingPrice?: number
  phoneNumber?: string
  number?: string
  service?: string
  serviceId?: number
  startTime?: string
  status?: string | null
  validConsultationFormId?: number
  noShow?: boolean
}
const useGetAllBookings = makeGetHook<IAppointment[]>(BOOKINGS.GET_ALL_BOOKINGS, fetcher)

const apis = {
  getBooking: () => fetcher.get(`${BOOKINGS.GET_ALL_BOOKINGS}`),
}

export default function useGetAllBookingsAPI<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getBooking())
  return parameters
}

export { useGetAllBookings, useGetAllBookingsAPI }
