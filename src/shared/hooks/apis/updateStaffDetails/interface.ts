// export interface IAddStaffDetailsPayload {
//   firstName: string
//   lastName: string
//   email: string
//   number: string
//   gender: 'MALE' | 'FEMALE' | 'OTHER'
//   role: 1 | 2 | 3 | 4
//   dateOfBirth: string
//   address: string
//   emergencyContactName: string
//   emergencyContactNumber: string
//   employeeBio: string
//   pin: string
//   employee_id: number
//   id: number
//   performedServices: number[]
//   employeeImg: any
//   defaultStaff: any
// }

import { IStaffDetails } from '../getAllStaff'

export type IAddStaffDetailsPayload = IStaffDetails
