import { AxiosRequestConfig } from 'axios'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUpdateFaqPayload {
  id: number
  title: string
  description: string
  status?: number
  priority?: number
}

export default {
  updateFaq: (payload?: IUpdateFaqPayload, config?: AxiosRequestConfig<IUpdateFaqPayload>) =>
    fetcher.put(`${SETTINGS.EDIT_FAQ}`, payload, config),
}
