import { AxiosRequestConfig } from 'axios'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IResetPinPayload {
  email: string
  newPin: string
  token: string
}

export default {
  resetPin: (payload?: IResetPinPayload, config?: AxiosRequestConfig<IResetPinPayload>) =>
    fetcher.post(STAFF.RESET_PIN, payload, config),
}
