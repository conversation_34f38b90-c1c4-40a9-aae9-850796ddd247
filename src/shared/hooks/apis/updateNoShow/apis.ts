import { AxiosRequestConfig } from 'axios'
import queryString from 'query-string'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucher {
  noShow: boolean
  appointment_id: number
  amount: string
}

export default {
  updateNoShow: (payload?: IVoucher, config?: AxiosRequestConfig<IVoucher>) => {
    const queryParams = payload ? queryString.stringify(payload) : ''
    return fetcher.post(`${BOOKINGS.UPDATE_NO_SHOW}?${queryParams}`, {})
  },
}
