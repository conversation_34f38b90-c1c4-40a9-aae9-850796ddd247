import { AxiosRequestConfig } from 'axios'
import { STRIPE } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ISendEmail {
  to?: string
  subject?: string
  content?: string | any
}

export default {
  sendEmail: (payload?: ISendEmail, config?: AxiosRequestConfig<ISendEmail>) =>
    fetcher.post(STRIPE.SEND_CUSTOME_EMAIL, payload, config),
}
