import useAxiosApi from '../useAxiosApi'
import { SALES } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAppointment {}

const apis = {
  getdailySummary: (period: string, employeeId?: string | number) => {
    const queryParams = new URLSearchParams({ period })

    if (employeeId) {
      queryParams.append('employee_id', String(employeeId))
    }

    return fetcher.get(`${SALES.TRANSACTION_SUMMARY}?${queryParams.toString()}`)
  },
}

export default function useDailySummaryAPI<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, { period: string; employee_id?: string | number }>((params) => {
    const period = params?.period ?? 'last week'
    const employeeId = params?.employee_id

    return apis.getdailySummary(period, employeeId)
  })

  return parameters
}

export { useDailySummaryAPI }
