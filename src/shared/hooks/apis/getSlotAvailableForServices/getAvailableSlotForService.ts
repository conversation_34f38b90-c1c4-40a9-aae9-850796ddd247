import apis, { IAvailableSlotPayload } from './apis'
import useAxiosApi from '../../useAxiosApi'

export interface IAvailableServiceDuration {
  workDate: string
  availableSlots: string[]
}

export default function useGetSlotAvailableForService<ResponseData = IAvailableServiceDuration[]>() {
  const parameters = useAxiosApi<ResponseData, IAvailableSlotPayload>(apis.getAvailableSlotForService)
  return parameters
}
