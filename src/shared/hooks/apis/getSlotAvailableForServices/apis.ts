import { AxiosRequestConfig } from 'axios'
import { SERVICES } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAvailableSlotPayload {
  employeeId: number
  serviceDuration: number
}

export default {
  getAvailableSlotForService: (payload?: IAvailableSlotPayload, config?: AxiosRequestConfig<IAvailableSlotPayload>) =>
    fetcher.get(
      `${SERVICES.GET_SLOT_AVAILABLE_FOR_SERVICE_DURATION}?employeeId=${payload?.employeeId}&serviceDuration=${payload?.serviceDuration}`,
      config,
    ),
}
