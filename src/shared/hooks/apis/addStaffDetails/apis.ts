import { AxiosRequestConfig } from 'axios'

import { IAddStaffDetailsPayload } from '../updateStaffDetails/interface'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export default {
  addStaffDetail: (payload?: IAddStaffDetailsPayload, config?: AxiosRequestConfig<IAddStaffDetailsPayload>) =>
    fetcher.post(STAFF.ADD_STAFF, payload, config),
}
