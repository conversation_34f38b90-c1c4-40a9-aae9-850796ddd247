import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { FEEDBACK } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IFeedback {
  clientId: number | string
  id: number
  client_id: number | string
  employeeId: number | string
  rating: number
  reviewText: string
  insertDate: string
}

const useGetAllFeedback = makeGetHook<IFeedback[]>(`${FEEDBACK.GET_ALL_FEEDBACK}`, fetcher)

const apis = {
  getFeedback: (clientId: string) => fetcher.get(`${FEEDBACK.GET_ALL_FEEDBACK}?clientId=${clientId}`),
}

export default function useGetAllFeedbackAPI<ResponseData = unknown>(clientId: string) {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getFeedback(clientId))
  return parameters
}
export { useGetAllFeedback, useGetAllFeedbackAPI }
