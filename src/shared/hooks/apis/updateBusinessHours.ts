import useAxiosApi from '../useAxiosApi'
import { IGetBusinessHours } from './getBusinessHours'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export default function useUpdateBusinessHours<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, IGetBusinessHours[]>((payload) =>
    fetcher.post(SETTINGS.UPDATE_BUSINESS_HOURS, payload),
  )
  return parameters
}
