import { AxiosRequestConfig } from 'axios'
import { BOOKINGS, USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUpdateBookingPayload {
  employeeId: number
  clientId: number
  id: number
  description: string
  endTime: string
  startTime: string
  isApp: boolean
  isAllDay: null
  customer: null
  service: string
  serviceId: number
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  pendingPrice: number
}

export default {
  updateBooking: (payload?: IUpdateBookingPayload, config?: AxiosRequestConfig<IUpdateBookingPayload>) =>
    fetcher.post(BOOKINGS.UPDATE_BOOKING, payload, config),
}
