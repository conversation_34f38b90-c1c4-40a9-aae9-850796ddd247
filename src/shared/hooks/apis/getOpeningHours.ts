import makeGetHook from '../makeGetHook/makeGetHook'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IGetBusinessHours {
  day: string
  open_status: boolean
  start_time: string
  end_time: string
}

const useGetOpeningHours = makeGetHook<IGetBusinessHours[]>(SETTINGS.GET_BUSINESS_HOURS, fetcher)

export { useGetOpeningHours }
