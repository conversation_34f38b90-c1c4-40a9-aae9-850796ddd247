import apis from './apis'
import useAxiosApi from '../../useAxiosApi'

export interface IUpdateUserDetailsPayload {
  email: string
  givenName: string
  familyName: string
  phoneMobile: string
  gender: 'MALE' | 'FEMALE' | 'OTHER'
  dateOfBirth: string
  address: string
  userGMID: number
}

export default function useUpdateUserDetails<ResponseData = unknown>() {
  const parameters = useAxiosApi<ResponseData, IUpdateUserDetailsPayload>(apis.updateUserDetail)
  return parameters
}
