import { AxiosRequestConfig } from 'axios'
import { IUpdateUserDetailsPayload } from './interface'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export default {
  /**

Create update user detail. */ updateUserDetail: (
    payload?: IUpdateUserDetailsPayload,
    config?: AxiosRequestConfig<IUpdateUserDetailsPayload>,
  ) => fetcher.post(USER.UPDATE_USER_DETAIL, payload, config),
}
