import { AxiosRequestConfig } from 'axios'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IAddUserPayload {
  email: string
  givenName: string
  familyName: string
  phoneMobile: string
  gender: 'MALE' | 'FEMALE' | 'OTHER'
  dateOfBirth: string
  address: string
}

export default {
  addUser: (payload?: IAddUserPayload, config?: AxiosRequestConfig<IAddUserPayload>) =>
    fetcher.post(USER.ADD_USER, payload, config),
}
