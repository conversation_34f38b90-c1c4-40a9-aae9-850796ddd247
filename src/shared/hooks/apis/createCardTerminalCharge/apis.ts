import { AxiosRequestConfig } from 'axios'
import { STRIPE } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ICreateCardTerminalChargePayload {
  paymentId: string
  amount: Number
  appointmentId?: number
  clientId?: number
  email?: string
  transactionType?: number
}

export default {
  createCardTerminalCharge: (
    payload?: ICreateCardTerminalChargePayload,
    config?: AxiosRequestConfig<ICreateCardTerminalChargePayload>,
  ) => fetcher.post(STRIPE.CREATE_CARD_TERMINAL_CHARGE_CHARGE, payload, config),
}
