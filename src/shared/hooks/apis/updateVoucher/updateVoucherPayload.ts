import { IVoucherItem } from '@/shared/hooks/apis/updateVoucher/apis'

interface voucherPayloadParams {
  selectedGiftVoucher: IVoucherItem
  amountUsed: number
}

export const voucherUpdatePayload = ({ selectedGiftVoucher, amountUsed }: voucherPayloadParams): IVoucherItem => {
  const remainingBalance = (selectedGiftVoucher?.remainingBalance ?? 0) - amountUsed

  return {
    ...selectedGiftVoucher,
    remainingBalance,
  }
}
