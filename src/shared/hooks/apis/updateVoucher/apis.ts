import { AxiosRequestConfig } from 'axios'
import { VOUCHER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucherItem {
  clientEmail?: string
  clientFirstName?: string
  clientSurname?: string
  description: string
  emailTo?: string
  expire: string
  initialBalance: number
  remainingBalance: number
  uniqueVoucherCode?: string
  dateCreated?: string | any
  isExpired?: boolean
  voucherId?: number
  isUsed?: boolean
  isVoucherEnabled?: boolean
}

export type IAddVoucherPayload = IVoucherItem[]

export default {
  updateVoucher: (payload?: IVoucherItem[], config?: AxiosRequestConfig<IVoucherItem[]>) =>
    fetcher.post(VOUCHER.UPDATE_VOUCHER, payload, config),
}
