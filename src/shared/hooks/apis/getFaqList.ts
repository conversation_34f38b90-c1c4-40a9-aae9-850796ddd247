import makeGetHook from '../makeGetHook/makeGetHook'
import { SETTINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IFaq {
  id: number
  title: string
  description: string
  status?: number
  priority?: number
  dateCreated?: string
}

const useGetFaqList = makeGetHook<IFaq[]>(SETTINGS.GET_FAQ_LIST, fetcher)

export { useGetFaqList }
