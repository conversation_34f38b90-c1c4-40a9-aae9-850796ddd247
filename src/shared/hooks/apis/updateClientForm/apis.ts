import { AxiosRequestConfig } from 'axios'
import { CATALOG } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUpdateClientFormPayload {
  id: number
  clientId: number | string | undefined
  formData: {}
  formId: number
  isFormComplete: number
  expiryDate: Date | string
  formDate: Date | string
}

export default {
  updateClientForm: (payload?: IUpdateClientFormPayload, config?: AxiosRequestConfig) => {
    const url = CATALOG.UPDATE_SURVEY_FORM
    return fetcher.put(url, payload, config)
  },
}
