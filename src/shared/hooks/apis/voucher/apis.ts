import { AxiosRequestConfig } from 'axios'
import { VOUCHER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IVoucher {
  flag: boolean
  voucherId: number
}

export default {
  enableVoucher: (payload?: IVoucher, config?: AxiosRequestConfig<IVoucher>) =>
    fetcher.put(`${VOUCHER.ENABLE_VOUCHER}?voucherId=${payload?.voucherId}&flag=${payload?.flag ? 1 : 0}`, {}),
}
