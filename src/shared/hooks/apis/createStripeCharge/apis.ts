import { AxiosRequestConfig } from 'axios'
import { STRIPE } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ICreateStripeChargePayload {
  email: string
  transactionType: number
  token: string
  amount: number
  description: string
  groupAppointmentIds: (string | undefined)[] | string
}

export default {
  createStripeCharge: (payload?: ICreateStripeChargePayload, config?: AxiosRequestConfig<ICreateStripeChargePayload>) =>
    fetcher.post(STRIPE.CREATE_STRIPE_CHARGE, payload, config),
}
