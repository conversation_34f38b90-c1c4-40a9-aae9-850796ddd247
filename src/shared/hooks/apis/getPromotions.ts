import makeGetHook from '../makeGetHook/makeGetHook'
import { PROMOTIONS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IPromotions {
  promotionId: number
  activePromo: boolean
  discountApplied: number
  maxCodeUsers: number
  startDate: number[]
  endDate: number[]
  voucherUsed: boolean
  codesUsed: number
  promoName: string | null
  promoCode: string | null
}

const useGetPromotions = makeGetHook<IPromotions>(PROMOTIONS.GET_PROMOTIONS, fetcher)

export { useGetPromotions }
