import { IAppointment } from './getAllBookings'
import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IBooking {
  clientId: number
  description: string
  endTime: string
  startTime: string
  eventType: string | null
  id: number
  customer: string
  service: string
  employeeId: number
  employeeName: string
  serviceId: number
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  isApp: boolean
  paymentReceipt: null
  colourCode: string
  categoryId: string
  categoryName: string
  outstandingBalance: number
  hasEmail: boolean
  groupAppointmentId: string
  validConsultationFormId: number
  pendingPrice: number
  paymentMethodId: string
  customerId: string
  status: string | null
  cancelled: boolean
  newUser: boolean
  pending: boolean
  allDay: boolean
}

const useGetBookingByClientId = makeGetHook<IAppointment[]>(BOOKINGS.GET_BOOKING_BY_CLIENT_ID, fetcher)

// * Get Record using Axios method
const apis = {
  getBookingByClientId: (id: number) => fetcher.get(`${BOOKINGS.GET_BOOKING_BY_CLIENT_ID}?userId=${id}`),
}

function useAxiosGetAllBookings() {
  const parameters = useAxiosApi<IAppointment[], {}>((id: any) => {
    return apis.getBookingByClientId(id)
  })
  return parameters
}

export { useGetBookingByClientId, useAxiosGetAllBookings }
