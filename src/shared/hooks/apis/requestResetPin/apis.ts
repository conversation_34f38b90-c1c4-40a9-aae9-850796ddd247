import { AxiosRequestConfig } from 'axios'
import { STAFF } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IRequestResetPinPayload {
  email: string
}

export default {
  requestResetPin: (payload?: IRequestResetPinPayload, config?: AxiosRequestConfig<IRequestResetPinPayload>) =>
    fetcher.post(STAFF.REQUEST_RESET_PIN, payload, config),
}
