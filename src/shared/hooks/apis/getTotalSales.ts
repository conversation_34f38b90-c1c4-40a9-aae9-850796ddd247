import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ISales {
  total_sales: number
  clientId: string
}
const useGetAllSales = makeGetHook<ISales>(`${USER.GET_TOTAL_SALES}`, fetcher)

const apis = {
  getSales: (clientId: string) => fetcher.get(`${USER.GET_TOTAL_SALES}?clientId=${clientId}`),
}

export default function useGetAllSalesAPI<ResponseData = unknown>(clientId: string) {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getSales(clientId))
  return parameters
}

export { useGetAllSales, useGetAllSalesAPI }
