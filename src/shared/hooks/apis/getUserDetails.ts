import makeGetHook from '../makeGetHook/makeGetHook'
import { USER } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IUserDetails {
  userGMID: number // Unique ID for the user
  givenName: string // User's first name
  familyName: string // User's last name
  email: string // User's email address
  phoneLandline: string | null // Landline number (nullable)
  phoneMobile: string | null // Mobile phone number (nullable)
  isApp: boolean | null // Flag indicating if the user is using the app (nullable)
  address: string // Full address
  dateOfBirth: string | null // Date of birth (nullable)
  gender: 'MALE' | 'FEMALE' | 'OTHER' // Gender, with restricted values
  outstandingBalance: number // Outstanding balance (can be 0)
  lastVisit: string // Date of the user's last visit
  isActive: boolean // User's active status
  bookingType: string | null // Type of booking (nullable)
  staffMember: string | null // Assigned staff member (nullable)
  bookingTime: string | null // Booking time (nullable)
  bookingSource: string | null // Source of booking (nullable)
  bookingService: string | null // Service for the booking (nullable)
  paymentStatus: string | null // Payment status (nullable)
}

const useGetUserDetails = makeGetHook<IUserDetails>(USER.GET_USER_DETAILS, fetcher)

export { useGetUserDetails }
