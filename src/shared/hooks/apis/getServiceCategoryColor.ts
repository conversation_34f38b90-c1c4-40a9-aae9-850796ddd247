import makeGetHook from '../makeGetHook/makeGetHook'
import { SERVICES } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IServiceCategoryColor {
  id: number
  colourCode: string
  colourText: string
}

const useGetServiceCategoryColor = makeGetHook<IServiceCategoryColor[]>(SERVICES.GET_SERVICE_CATEGORY_COLOR, fetcher)

export { useGetServiceCategoryColor }
