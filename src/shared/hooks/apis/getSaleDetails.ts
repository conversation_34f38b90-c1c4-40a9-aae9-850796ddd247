import useAxiosApi from '../useAxiosApi'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ISaleDetails {
  firstName: string
  lastName: string
  clientEmail: string
  services: IService[]
}
export interface IService {
  serviceName: string
  employeeName: string
  transactionTypeId: number
  groupAppointmentId: string
  tipAmount: number | null
  duration: string
  originalPrice: number
  totalPendingPrice: number
  isPricePending: number
  transactions: ITransaction[]
}
export interface ITransaction {
  paymentMode: string
  transactionDate: string
  paymentAmount: number
}

const apis = {
  getSaleDetails: (ids: any) => {
    const idsParam = Array.isArray(ids) ? ids.join(',') : ids
    return fetcher.get(`${BOOKINGS.GET_SALE_DETAILS}?appointmentId=${idsParam}`)
  },
}

function useAxiosGetSaleDetails(id?: number) {
  const parameters = useAxiosApi<ISaleDetails, {}>((id: any) => {
    return apis.getSaleDetails(id)
  })
  return parameters
}

export { useAxiosGetSaleDetails }
