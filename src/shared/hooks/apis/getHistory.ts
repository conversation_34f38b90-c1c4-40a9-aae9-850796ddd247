import makeGetHook from '../makeGetHook/makeGetHook'
import useAxiosApi from '../useAxiosApi'
import { HISTORY } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface IHistory {
  clientId: string
  appointmentId: number
  canceled: boolean
  endTimeExpected: string
  dateCreated: string
}
const useGetAllHistory = makeGetHook<IHistory[]>(`${HISTORY.GET_HISTORY}`, fetcher)

const apis = {
  getHistory: (clientId: string) => fetcher.get(`${HISTORY.GET_HISTORY}?clientId=${clientId}`),
}

export default function useGetAllHistoryAPI<ResponseData = unknown>(clientId: string) {
  const parameters = useAxiosApi<ResponseData, {}>(() => apis.getHistory(clientId))
  return parameters
}

export { useGetAllHistory, useGetAllHistoryAPI }
