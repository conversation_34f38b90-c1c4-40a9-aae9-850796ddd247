import { AxiosRequestConfig } from 'axios'
import { IStripeCreateCashChargePayload } from './interface'
import { APPOINTMENT_CHECKOUT } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export default {
  stripeCreateCashCharge: (
    payload?: IStripeCreateCashChargePayload,
    config?: AxiosRequestConfig<IStripeCreateCashChargePayload>,
  ) => fetcher.post(APPOINTMENT_CHECKOUT.STRIPE_CREATE_CASH_CHARGE, payload, config),
}
