import { AxiosRequestConfig } from 'axios'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface ISaveBookingWithPaymentPayload {
  employeeId: string
  clientId: number
  description: string
  amount: string
  startTime: string
  endTime: string
  isApp: boolean
  id: number
  isAllDay: boolean
  customer: null
  service: string
  serviceId: number
  paymentMethodId: string
  paymentReceipt: string
  stripeEmail: string
  transactionType: string
  stripeDescription: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
}

export default {
  saveBookingWithPayment: (
    payload?: ISaveBookingWithPaymentPayload[],
    config?: AxiosRequestConfig<ISaveBookingWithPaymentPayload[]>,
  ) => fetcher.post(BOOKINGS.SAVE_BOOKING_WITH_PAYMENT, payload, config),
}
