import { useCallback, useState } from 'react'
import useMarkAsRead from './apis/notifications'
import { useWebSocket } from './useWebSocket'
import { NOTIFICATIONS } from '@/shared/constant/apiEndpoints'
import { fetcher } from '@/shared/utils/api/axiosInstance'

export interface Notification {
  app_startTime: string
  clientName: string
  app_serviceName: string
  staffName: string
  isRead: boolean
  id: number
  notificationType: string
  bookingId: string
  createDateTime: string
}

interface UseNotificationsOptions {
  websocketUrl?: string
  isLoggedIn?: boolean // Flag to control whether notifications should be active
}

/**
 * Hook for managing notifications
 * - Connects to WebSocket for real-time notifications only when user is logged in
 * - Manages notification state with safe message parsing
 * - Provides functions to mark notifications as read
 * - Automatically disconnects on logout to prevent unauthorized access
 * - Cleans up resources when unmounted
 */
export function useNotifications(options?: UseNotificationsOptions) {
  const {
    websocketUrl = 'wss://dev3-sleet.gomanage.dev/websocket',
    isLoggedIn = false, // Default to not connected until explicitly logged in
  } = options || {}
  const [notifications, setNotifications] = useState<Notification[]>([])
  const { execute: markAsReadApi } = useMarkAsRead()
  const [isMarkingAllAsRead, setIsMarkingAllAsRead] = useState(false)

  // Handle WebSocket messages with robust error handling
  // Only processes messages when user is logged in
  const handleWebSocketMessage = useCallback((data: any) => {
    try {
      // Handle different possible data formats
      let notificationsData: any[] = []

      // Extract notifications from various data formats
      if (data && Array.isArray(data.data)) {
        notificationsData = data.data
      } else if (data && Array.isArray(data)) {
        notificationsData = data
      } else if (typeof data === 'string') {
        try {
          const parsedData = JSON.parse(data)
          if (parsedData && Array.isArray(parsedData.data)) {
            notificationsData = parsedData.data
          } else if (parsedData && Array.isArray(parsedData)) {
            notificationsData = parsedData
          } else if (parsedData && typeof parsedData === 'object') {
            notificationsData = [parsedData]
          }
        } catch (parseError) {
          console.error('Error parsing string data:', parseError)
        }
      } else if (data && data.data) {
        try {
          const parsedEventData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data
          if (parsedEventData && Array.isArray(parsedEventData.data)) {
            notificationsData = parsedEventData.data
          } else if (parsedEventData && Array.isArray(parsedEventData)) {
            notificationsData = parsedEventData
          } else if (parsedEventData && typeof parsedEventData === 'object') {
            notificationsData = [parsedEventData]
          }
        } catch (parseError) {
          console.error('Error parsing event data:', parseError)
        }
      }

      // Filter for valid notifications (must have an id)
      const validNotifications = notificationsData.filter((item: any) => item && typeof item.id === 'number')

      if (validNotifications.length === 0) {
        return // Silently ignore invalid data
      }

      // Update notifications state, avoiding duplicates
      setNotifications((currentNotifications) => {
        const newNotifications = validNotifications.filter(
          (newNotification: Notification) =>
            !currentNotifications.some((existing) => existing.id === newNotification.id),
        )

        return newNotifications.length > 0 ? [...newNotifications, ...currentNotifications] : currentNotifications
      })
    } catch (error) {
      // Log errors without breaking the hook
      console.error('Error processing notification data:', error)
    }
  }, [])

  // Initialize WebSocket connection only when user is logged in
  const { isConnected } = useWebSocket({
    url: websocketUrl,
    onMessage: handleWebSocketMessage,
    isEnabled: isLoggedIn, // Only connect when user is logged in
  })

  // Mark a single notification as read
  const markAsRead = useCallback(
    async (notificationId: number): Promise<void> => {
      try {
        // Call the API to mark the notification as read
        await markAsReadApi({ notification_id: notificationId })

        // Update local state
        setNotifications((currentNotifications) =>
          currentNotifications.map((notification) =>
            notification.id === notificationId ? { ...notification, isRead: true } : notification,
          ),
        )
      } catch (error) {
        console.error(`Failed to mark notification ${notificationId} as read:`, error)
      }
    },
    [markAsReadApi],
  )

  // Mark all notifications as read
  const markAllAsRead = useCallback(async (): Promise<void> => {
    const unreadNotifications = notifications.filter((notification) => !notification.isRead)

    if (unreadNotifications.length === 0 || isMarkingAllAsRead) return

    setIsMarkingAllAsRead(true)

    try {
      // Process notifications in batches to control concurrency
      const batchSize = 5
      const batches = []

      for (let i = 0; i < unreadNotifications.length; i += batchSize) {
        batches.push(unreadNotifications.slice(i, i + batchSize))
      }

      // Process each batch sequentially, but notifications within a batch in parallel
      for (const batch of batches) {
        await Promise.all(batch.map((notification) => markAsRead(notification.id)))
      }

      console.log(`Marked ${unreadNotifications.length} notifications as read`)
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    } finally {
      setIsMarkingAllAsRead(false)
    }
  }, [notifications, markAsRead, isMarkingAllAsRead])

  // Calculate the number of unread notifications
  const unreadCount = notifications.filter((notification) => !notification.isRead).length

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    isConnected,
    isMarkingAllAsRead,
  }
}
