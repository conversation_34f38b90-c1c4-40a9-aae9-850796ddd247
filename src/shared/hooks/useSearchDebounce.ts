// import debounce from 'lodash.debounce';
import { useEffect, useMemo, useRef } from 'react'
import { debounce } from 'lodash'

const useSearchDebounce = (callback: Function) => {
  const ref: any = useRef(null)

  useEffect(() => {
    ref.current = callback
  }, [callback])

  const debouncedCallback = useMemo(() => {
    const func = () => {
      ref.current?.()
    }

    return debounce(func, 1000)
  }, [])

  return debouncedCallback
}

export default useSearchDebounce
