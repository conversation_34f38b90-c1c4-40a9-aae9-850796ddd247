import { useCallback } from 'react'
import queryString, { StringifiableRecord } from 'query-string'
import objectCheckNull from '@/shared/utils/api/objectCheckNull'
import replacer from '@/shared/utils/api/replacer'

export interface Variables {
  [key: string]: string | undefined
}

export type UseKeyArgs<PathParams extends Variables> = {
  defaultPathParams?: PathParams
  defaultQueryParams?: StringifiableRecord
  pathParams?: PathParams
  queryParams?: StringifiableRecord
  disableFetch?: boolean
  urlPattern: string
}

export default function useKey<PathParams extends Variables>({
  defaultPathParams,
  defaultQueryParams,
  pathParams,
  queryParams,
  disableFetch,
  urlPattern,
}: UseKeyArgs<PathParams>) {
  const getKey = useCallback(
    (cusQueryParams?: StringifiableRecord) => {
      const mergePathParams = {
        ...defaultPathParams,
        ...pathParams,
      } as PathParams
      const mergeQuery = {
        ...defaultQueryParams,
        ...queryParams,
        ...cusQueryParams,
      } as StringifiableRecord
      if (disableFetch || objectCheckNull(mergePathParams, { checkEmptyString: true })) {
        return null
      }
      return `${replacer<PathParams>(urlPattern, mergePathParams)}?${queryString.stringify(mergeQuery)}`
    },
    [defaultPathParams, defaultQueryParams, disableFetch, pathParams, queryParams, urlPattern],
  )

  return getKey
}
