import { useCallback } from 'react'
import { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import { StringifiableRecord } from 'query-string'
import useSWR, { SWRConfiguration } from 'swr'
import useSWRImmutable from 'swr/immutable'
import useKey from './useKey'
import { Variables } from '@/shared/utils/api/replacer'
import { PathParams as DefaultPathParamsType, ReturnedValues } from '@/typings/apis'

/**
 * Maps HTTP status codes to user-friendly error messages.
 */
const STATUS_MESSAGES: Record<number, string> = {
  422: 'Incomplete data input, please double-check.',
  403: 'You do not have access permission.',
  400: 'System error, please contact the administrator.',
  404: 'No data found.',
  500: 'Server error, we will fix it as soon as possible.',
  502: 'Bad gateway, retrying...',
}

/**
 * Factory function to create a reusable data-fetching hook with SWR.
 * @template Data - Type of the response data.
 * @template PathParams - Type of the path parameters.
 * @template ErrorData - Type of the error data.
 */
export default function makeGetHook<
  Data = unknown,
  PathParams extends Variables = DefaultPathParamsType,
  ErrorData = unknown,
>(
  urlPattern: string,
  fetcher: AxiosInstance,
  defaultPathParams?: PathParams,
  defaultQueryParams?: StringifiableRecord,
  defaultConfig?: SWRConfiguration<AxiosResponse<Data>, AxiosError<ErrorData>>,
  immutable?: boolean,
) {
  const useSelectedSWR = !immutable ? useSWRImmutable : useSWR

  return function useItem(
    pathParams?: PathParams,
    queryParams?: StringifiableRecord,
    customConfig?: SWRConfiguration<AxiosResponse<Data>, AxiosError<ErrorData>>,
    disableFetch = false,
  ): ReturnedValues<Data, ErrorData> {
    // Generate a fetch key based on parameters
    const getKey = useKey<PathParams>({
      defaultPathParams,
      defaultQueryParams,
      pathParams,
      queryParams,
      disableFetch,
      urlPattern,
    })

    const key = getKey()

    // Axios fetcher function
    const fetchData = useCallback((url: string) => fetcher.get<Data>(url), [fetcher])

    // SWR hook for data fetching
    const { error, data, mutate, isValidating, isLoading } = useSelectedSWR(key, fetchData, {
      ...defaultConfig,
      ...customConfig,
    })

    /**
     * Retrieves a user-friendly error message based on the HTTP status code.
     */
    const getErrorMessage = useCallback(
      (status: number): string => STATUS_MESSAGES[status] || 'An unknown error occurred.',
      [],
    )

    // Handle errors (e.g., logging, showing notifications)
    const handleErrors = useCallback(() => {
      if (!error || !error.response || isValidating) return

      const status = error.response?.status
      const errorMessage = getErrorMessage(status)

      console.error(`Error ${status}: ${errorMessage}`, error.response?.data)

      // Add error handling logic here (e.g., log errors, show toasts, etc.)
      // Example: showSnackbar({ message: errorMessage, severity: 'error' });
    }, [error, isValidating, getErrorMessage])

    // Call error handling whenever `error` changes
    if (error) handleErrors()

    return {
      data: data?.data,
      isError: !!error,
      mutate,
      response: data,
      error,
      key,
      isValidating,
      isLoading,
    }
  }
}
