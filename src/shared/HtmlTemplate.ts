export const VoucherTemplate = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Gift Voucher</title>
    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans:400,700|Rubik:400,700,300|Courgette:400,700,300"
      rel="stylesheet"
      type="text/css"
    />
  </head>

  <body style="margin: 0; padding: 0; background-color: #ffffff; font-family: 'Open Sans', sans-serif">
    <table
      style="
        background-color: #ffffff;
        max-width: 320px;
        border-radius: 16px;
        text-align: center;
        overflow: hidden;
        margin: 0 auto;
        font-family: 'Open Sans', sans-serif;
        border: 2px solid #320A57;
      "
    >
      <tr>
        <td style="padding: 20px 20px">
          <div style="text-align: center">
            <img
                src={{APP_LOGO}}
                style="width: 150px; height: auto; display: block; margin: auto"
              />
          </div>
          <h2 style="font-size: 20px; color: #333333; margin: 0px; letter-spacing: 4px">GIFT VOUCHER</h2>
          <h1 style="font-size: 24px; color: #000000; margin: 10px 0px; font-weight: 900">A GIFT FOR YOU!</h1>
          <p style="font-size: 48px; color: #320A57; margin: 20px 0; font-weight: 800">€ {{TOTAL_PAYMENT}}</p>

          <div style="margin-top: 30px">
            <p style="font-size: 16px; color: #000000; margin-bottom: 6px">Voucher Number:</p>
            <div
              style="
                border: 2px solid #320A57;
                border-radius: 8px;
                padding: 10px;
                font-size: 16px;
                color: #320A57;
                display: inline-block;
              "
            >
              {{VOUCHER_CODE}}
            </div>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>`

export const ReceiptTemplate = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Receipt</title>
    <link href="https://fonts.googleapis.com/css?family=Rubik:400,700,300" rel="stylesheet" type="text/css" />
    <link href="https://fonts.googleapis.com/css?family=Courgette:400,700,300" rel="stylesheet" type="text/css" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,700" rel="stylesheet" type="text/css" />
  </head>

  <body>
    <table align="center" style="width: 620px; height: 100%; border-collapse: collapse; border: 2px solid #320A57">
      <tr>
        <td style="padding: 20px 20px;">
          <div style="color: black">
            <table style="width: 100%; margin-top: 20px; color: black">
              <tr>
                <td colspan="2" align="center">
                  <img src={{LOGO_IMAGE_URL}} alt="logo" width="150px" />
                </td>
              </tr>
              <tr>
                <td>
                  <p>STATUS:</p>
                </td>
                <td style="color: {{STATUS_TEXT_COLOR}}">
                  <p>{{STATUS}}</p>
                </td>
                <td></td>
              </tr>
              <tr>
                <td>
                  <p>RECEIPT TO:</p>
                </td>
                <td>
                  <p>{{RECEIPT_TO}}</p>
                </td>
              </tr>
              <tr>
                <td>
                  <p>CLIENT NAME:</p>
                </td>
                <td>
                  <p>{{CLIENT_NAME}}</p>
                </td>
              </tr>
            </table>
            <table style="width: 100%; margin-top: 20px; border-collapse: collapse">
              <tbody>
                {{SERVICE_WITH_PAYMENTS}}
              </tbody>
            </table>
            <table style="width: 100%; margin-top: 10px; border-collapse: collapse">
              <tr style="border-top: solid 1px black">
                <td style="text-align: left">TOTAL SERVICE AMOUNT</td>
                <td style="text-align: right">€{{TOTAL_SERVICE_AMOUNT}}</td>
              </tr>
            </table>
            <table style="width: 100%; margin-top: 10px; border-collapse: collapse">
              <tr style="border-top: solid 1px black">
                <td style="text-align: left">TOTAL PAID AMOUNT</td>
                <td style="text-align: right; padding-right: 20px;">€{{TOTAL_PAID_AMOUNT}}</td>
              </tr>
            </table>
            <table style="width: 100%; margin-top: 20px; border-collapse: collapse; padding-right: 20px">
              <tr style="font-size: 18px; font-weight: 600">
                <td style="text-align: left">TOTAL BALANCE DUE</td>
                <td style="text-align: right">€{{TOTAL_DUE_AMOUNT}}</td>
              </tr>
            </table>
            <table style="width: 100%; margin-top: 20px; border-collapse: collapse; color: black">
              <tr>
                <td>
                  <p>DATE:</p>
                </td>
                <td>
                  <p>{{TRANSACTION_DATE}}</p>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>
`
