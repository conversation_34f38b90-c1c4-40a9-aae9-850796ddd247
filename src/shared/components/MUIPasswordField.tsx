import { useState } from 'react'
import { InputAdornment, TextFieldProps, TextField as TF } from '@mui/material'
import { Box } from '@mui/system'
import { MUIStyled } from './MUIStyled'

const TextField = MUIStyled(TF)(({ theme }) => ({
  marginTop: 0,
  marginBottom: 0,
  '& .MuiInputBase-root': {
    borderRadius: 12,
    backgroundColor: theme.palette.white?.main,
    borderColor: theme.palette.gray?.main,
    boxShadow: `0px 1px 2px 0px ${theme.palette.gray?.light}0D`,
    overflow: 'hidden',
    '&.MuiInputBase-multiline': {
      padding: 0,
    },
    '&.MuiOutlinedInput-root': {
      paddingRight: '10px',
      '& .MuiInputBase-input': {
        fontSize: 16,
        padding: '9px 12px 10px',
        color: theme.palette.gray?.light,
        '&::placeholder': {
          textTransform: 'capitalize',
        },
        '&::-webkit-outer-spin-button, &::-webkit-inner-spin-button': {
          WebkitAppearance: 'none',
          appearance: 'none',
        },
      },
      '& .MuiInputAdornment-root': {
        marginRight: 0,
        '& i': {
          color: theme.palette.gray?.light,
          marginLeft: 5,
        },
        '& .MuiTypography-root': {
          fontSize: 16,
          color: theme.palette.gray?.light,
          borderRight: `1px solid ${theme.palette.gray?.main}`,
          paddingRight: 10,
          lineHeight: 3,
        },
      },
    },
    '&.Mui-focused': {
      boxShadow: `0px 0px 2px 0 ${theme.palette.primary.light100}80`,
      '& .MuiOutlinedInput-notchedOutline': {
        borderWidth: 1,
        borderColor: theme.palette.primary.light200,
      },
    },
    '&:hover': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.light200,
      },
    },
  },
  '& .MuiFormHelperText-root': {
    margin: 0,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '& svg': {
      width: 15,
      height: 15,
      verticalAlign: 'middle',
    },
  },
}))

interface PasswordFieldProps extends Omit<TextFieldProps, 'type'> {}

const MUIPasswordField = ({ placeholder = 'Enter password', ...rest }: PasswordFieldProps) => {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <TextField
      {...rest}
      placeholder={placeholder}
      type={showPassword ? 'text' : 'password'}
      InputProps={{
        ...rest.InputProps,
        endAdornment: (
          <InputAdornment position="end">
            <Box
              onClick={() => setShowPassword((prev) => !prev)}
              sx={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <i className={showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} />
            </Box>
          </InputAdornment>
        ),
      }}
    />
  )
}

export default MUIPasswordField
