import React, { useState } from 'react'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import {
  Select,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Checkbox,
  ListItemText,
  ClickAwayListener,
  MenuItem,
} from '@mui/material'
import M<PERSON><PERSON>orm<PERSON>abel from './MUIFormLabel'
import { StyleSelect } from './MUISelect'
import MUIStack from './MUIStack'
import { MUIStyled } from './MUIStyled'

const StyledAccordion = MUIStyled(Accordion)(({ theme }) => ({
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.gray?.main}`,
  '&:before': {
    content: 'normal',
  },
  '&.Mui-expanded': {
    margin: 0,
  },
  '& .MuiButtonBase-root': {
    '&.Mui-expanded': {
      minHeight: 48,
      '& .MuiAccordionSummary-content': {
        margin: 0,
      },
    },
  },
}))

export interface MUIMultiSelectOptionProps {
  group: string
  label: string
  value: string
}

interface MUIMultiSelectProps {
  label: string
  groupedOptions: MUIMultiSelectOptionProps[]
  selectedOptions: string[]
  onChange: (selectedOptions: string[]) => void
  placeholder?: string
  [key: string]: any
}

const MUIMultiSelect: React.FC<MUIMultiSelectProps> = ({
  label,
  groupedOptions,
  selectedOptions,
  onChange,
  placeholder = '',
  ...props
}) => {
  const [open, setOpen] = useState(false)

  const handleSelectionChange = (option: string) => {
    const newSelection = selectedOptions.includes(option)
      ? selectedOptions.filter((item) => item !== option)
      : [...selectedOptions, option]

    onChange(newSelection)
  }

  const groupedData = groupedOptions.reduce<Record<string, MUIMultiSelectOptionProps[]>>((acc, option) => {
    acc[option.group] = acc[option.group] || []
    acc[option.group].push(option)
    return acc
  }, {})

  const handleToggleDropdown = () => setOpen((prev) => !prev)

  const handleCloseDropdown = () => setOpen(false)

  const preventClose = (event: React.MouseEvent) => {
    event.stopPropagation()
  }

  return (
    <ClickAwayListener onClickAway={handleCloseDropdown}>
      <StyleSelect fullWidth>
        {label && <MUIFormLabel>{label}</MUIFormLabel>}
        <Select
          multiple
          value={selectedOptions}
          open={open}
          onClick={handleToggleDropdown}
          IconComponent={(props) => <i className="ri-arrow-down-s-line"></i>}
          renderValue={(selected) => {
            if (selected.length === 0) {
              return placeholder
            }
            const selectedLabels = groupedOptions
              .filter((option) => selected.includes(option.value))
              .map((option) => option.label)
            return selectedLabels.join(', ')
          }}
          displayEmpty={!!placeholder}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
                width: 300,
              },
              onMouseDown: preventClose,
            },
          }}
          {...props}
        >
          <MenuItem
            style={{ padding: 0 }}
            disableRipple
            sx={{
              ':hover': {
                backgroundColor: 'white !important',
              },
            }}
          >
            <div onMouseDown={preventClose} style={{ width: '100%' }}>
              {Object.keys(groupedData).map((group) => (
                <StyledAccordion key={group} onClick={(e) => e.stopPropagation()}>
                  <AccordionSummary
                    sx={{
                      '& .MuiAccordionSummary-content': {
                        fontSize: '16px !important',
                      },
                    }}
                    expandIcon={<ExpandMoreIcon />}
                  >
                    {group}
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{
                      '& .MuiTypography-root.MuiTypography-body1.MuiListItemText-primary': {
                        fontSize: '14px !important',
                      },
                    }}
                  >
                    {groupedData[group].map((option) => (
                      <MUIStack
                        key={option.value}
                        alignItems="center"
                        direction="row"
                        onClick={() => handleSelectionChange(option.value)}
                      >
                        <Checkbox
                          checked={selectedOptions.includes(option?.value)}
                          onChange={() => handleSelectionChange(option?.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <ListItemText sx={{ whiteSpace: 'normal' }} primary={option.label} />
                      </MUIStack>
                    ))}
                  </AccordionDetails>
                </StyledAccordion>
              ))}
            </div>
          </MenuItem>
        </Select>
      </StyleSelect>
    </ClickAwayListener>
  )
}

export default MUIMultiSelect
