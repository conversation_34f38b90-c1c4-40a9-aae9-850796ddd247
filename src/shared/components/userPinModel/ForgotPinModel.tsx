import { useState, useEffect } from 'react'
import { Box, Modal, Link } from '@mui/material'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import * as Yup from 'yup'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../MUIButton'
import MUI<PERSON><PERSON> from '../MUIGrid'
import M<PERSON>Select from '../MUISelect'
import MUIStack from '../MUIStack'
import MUITypography from '../MUITypography'
import ResetPinModel from './ResetPinModel'
import { generateResetUrl } from '@/shared/helper'
import { IStaffDetails, useGetAllStaff } from '@/shared/hooks/apis/getAllStaff'
import useRequestResetPin from '@/shared/hooks/apis/requestResetPin/requestResetPin'
import theme from '@/styles/theme'

interface ForgotModalProps {
  open: boolean
  setForgotModelOpen: (open: boolean) => void
  handleCancel: () => void
}

type OptionType = {
  value: number
  label: string
}

const validationSchema = Yup.object({
  employee_id: Yup.string().required('Please select a staff member'),
})

const ForgotPinModel: React.FC<ForgotModalProps> = ({ open, setForgotModelOpen, handleCancel }) => {
  const { data: staff } = useGetAllStaff()
  const { execute: requestResetPin } = useRequestResetPin()
  const [staffOptions, setStaffOptions] = useState<OptionType[]>([])
  const [staffMap, setStaffMap] = useState<Record<number, IStaffDetails>>({})
  const [resetPinModelopen, setResetPinModel] = useState(false)
  const [resetUrl, setResetUrl] = useState<string | null>(null)
  const [selectedEmail, setSelectedEmail] = useState<string>('')

  useEffect(() => {
    if (staff && staff.length > 0) {
      const options = staff.map((staffMember) => ({
        value: staffMember?.employee_id,
        label: `${staffMember?.firstName} ${staffMember?.lastName}`,
      }))

      const staffMapping = staff.reduce((acc: any, staffMember) => {
        acc[staffMember.employee_id] = staffMember
        return acc
      }, {})

      setStaffOptions(options)
      setStaffMap(staffMapping)
    }
  }, [staff])

  const [isSubmitting, setIsSubmitting] = useState(false)

  const formik = useFormik({
    initialValues: {
      employee_id: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setIsSubmitting(true)
        setResetUrl(null)

        const selectedStaffId = Number(values.employee_id)
        const selectedStaff = staffMap[selectedStaffId]
        setSelectedEmail(selectedStaff.email)

        if (!selectedStaff || !selectedStaff.email) {
          return
        }

        const url = generateResetUrl(values.employee_id, selectedStaff.email)
        setResetUrl(url)

        const payload = {
          email: selectedStaff.email,
        }

        try {
          await requestResetPin(payload)
          toast.success('Pin reset request successfully send to email.')
        } catch (apiError) {
          toast.error('PIN reset request fail.')
        }
      } catch (error) {
        toast.error('An error occurred, but a reset link has been generated.')

        // Try to generate URL even if there was an error elsewhere
        try {
          const selectedStaffId = Number(values.employee_id)
          const selectedStaff = staffMap[selectedStaffId]
          if (selectedStaff && selectedStaff.email) {
            const url = generateResetUrl(values.employee_id, selectedStaff.email)
            setResetUrl(url)
          }
        } catch (urlError) {}
      } finally {
        setForgotModelOpen(false)
        setIsSubmitting(false)
        setResetPinModel(true)
      }
    },
  })

  // const handleResetUrlClick = (e: React.MouseEvent) => {
  //   e.preventDefault()
  //   if (resetUrl) {
  //     window.open(resetUrl, '_blank')
  //   }
  // }

  useEffect(() => {
    formik.resetForm()
    setResetUrl(null)
  }, [open])

  return (
    <>
      <Modal
        open={open}
        disableEscapeKeyDown={true}
        disableAutoFocus={true}
        disableEnforceFocus={true}
        aria-labelledby="forgot-pin-modal"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
        }}
      >
        <Box
          sx={{
            width: 400,
            outline: 'none',
            bgcolor: 'background.paper',
            borderRadius: 4,
            boxShadow: 24,
            p: 3,
            m: '0px 20px',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <form onSubmit={formik.handleSubmit}>
            <MUITypography variant="h5" fontWeight={600}>
              Forgot PIN
            </MUITypography>
            <MUITypography variant="body2">Enter your user name to reset your PIN.</MUITypography>

            <MUIGrid container mt={2}>
              <MUIGrid size={{ xs: 12 }} mb={2}>
                <MUISelect
                  name="employee_id"
                  value={formik.values.employee_id}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  options={staffOptions}
                  placeholder="Select staff member"
                  error={formik.touched.employee_id && Boolean(formik.errors.employee_id)}
                  helperText={formik.touched.employee_id && formik.errors.employee_id}
                />
              </MUIGrid>
            </MUIGrid>

            <MUIStack display="flex" flexDirection="row" gap={2} mt={2}>
              <MUIButton
                sx={{
                  borderRadius: 2.5,
                  minWidth: 140,
                  width: '100%',
                  backgroundColor: theme.palette.danger?.main,
                }}
                variant="contained"
                type="button"
                onClick={handleCancel}
              >
                Cancel
              </MUIButton>
              <MUIButton
                sx={{
                  borderRadius: 2.5,
                  minWidth: 140,
                  backgroundColor: formik.isValid && formik.dirty ? 'primary.main' : 'gray',
                  width: '100%',
                }}
                variant="contained"
                type="submit"
                disabled={!formik.isValid || !formik.dirty || isSubmitting}
              >
                Reset
              </MUIButton>
            </MUIStack>

            {/* {resetUrl && (
              <Box mt={3} p={2} bgcolor="background.default" borderRadius={1}>
                <Link
                  href={resetUrl}
                  onClick={handleResetUrlClick}
                  sx={{
                    wordBreak: 'break-all',
                    color: theme.palette.primary.main,
                    cursor: 'pointer',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {resetUrl}
                </Link>
              </Box>
            )} */}
          </form>
        </Box>
      </Modal>
      <ResetPinModel
        open={resetPinModelopen}
        setResetPinModel={setResetPinModel}
        handleCancel={handleCancel}
        email={selectedEmail}
      />
    </>
  )
}

export default ForgotPinModel
