import { useState, useEffect } from 'react'
import { Box, Modal } from '@mui/material'
import { useFormik } from 'formik'
import { toast } from 'sonner'
import * as Yup from 'yup'
import <PERSON><PERSON><PERSON>utton from '../MUIButton'
import MUI<PERSON>rid from '../MUIGrid'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../<PERSON>UIPasswordField'
import M<PERSON><PERSON>tack from '../MUIStack'
import <PERSON><PERSON><PERSON><PERSON>t<PERSON><PERSON> from '../MUITextField'
import MUITypography from '../MUITypography'
import { useGetAllStaff } from '@/shared/hooks/apis/getAllStaff'
import useResetPin from '@/shared/hooks/apis/resetPin/resetPin'
import theme from '@/styles/theme'

interface ResetPinModalProps {
  open: boolean
  setResetPinModel: (open: boolean) => void
  handleCancel: () => void
  onPinReset?: (pin: string) => void // Optional callback for successful PIN reset
  email?: string
}

const validationSchema = Yup.object({
  pin: Yup.string()
    .matches(/^\d{6}$/, 'PIN must be exactly 6 digits')
    .required('New PIN is required'),
  ConfirmPin: Yup.string()
    .oneOf([Yup.ref('pin')], 'PINs must match')
    .required('Please confirm the new PIN'),
  token: Yup.string()
    .matches(/^\d{6}$/, 'Token must be exactly 6 digits')
    .required('Reset token is required'),
})

const ResetPinModel: React.FC<ResetPinModalProps> = ({ open, setResetPinModel, handleCancel, onPinReset, email }) => {
  const { mutate: fetchStaff } = useGetAllStaff()
  const { execute: resetPin } = useResetPin()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const formik = useFormik({
    initialValues: {
      pin: '',
      ConfirmPin: '',
      token: '',
    },
    validationSchema,
    enableReinitialize: true, // This allows the form to reinitialize when email prop changes
    onSubmit: async (values) => {
      try {
        setIsSubmitting(true)

        const payload = {
          email: email || '',
          newPin: values.pin,
          token: values.token,
        }

        const response = await resetPin(payload)
        toast.success('PIN reset successfully.')

        if (onPinReset) {
          onPinReset(values.pin)
        }
        fetchStaff()
        formik.resetForm()
        setResetPinModel(false)
        setTimeout(() => {
          handleCancel()
        }, 1500)
      } catch (error: any) {
        toast.error(error?.response?.data?.message || 'PIN reset failed. Please try again.')
      } finally {
        setIsSubmitting(false)
      }
    },
  })

  useEffect(() => {
    if (open) {
      formik.resetForm()
    }
  }, [open])

  const handleCloseResetPinModel = () => {
    setResetPinModel(false)
    handleCancel()
  }

  return (
    <Modal
      open={open}
      disableEscapeKeyDown={true}
      disableAutoFocus={true}
      disableEnforceFocus={true}
      aria-labelledby="forgot-pin-modal"
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <Box
        sx={{
          width: 400,
          outline: 'none',
          bgcolor: 'background.paper',
          borderRadius: 4,
          boxShadow: 24,
          p: 3,
          m: '0px 20px',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <form onSubmit={formik.handleSubmit}>
          <MUITypography variant="h5" fontWeight={600}>
            Reset Pin
          </MUITypography>
          <MUITypography variant="body2">Enter your email, reset token, and new pin.</MUITypography>

          <MUIGrid container mt={2}>
            <MUIGrid size={{ xs: 12 }}>
              <MUITextField
                name="token"
                placeholder="Enter token"
                fullWidth
                value={formik.values.token}
                onChange={(e) => {
                  const numericValue = e.target.value.replace(/\D/g, '')
                  formik.setFieldValue('token', numericValue)
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.token && Boolean(formik.errors.token)}
                helperText={formik.touched.token && formik.errors.token}
                inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
              />
            </MUIGrid>
            <MUIGrid size={{ xs: 12 }} mb={2} position="relative">
              <MUIPasswordField
                name="pin"
                placeholder="New pin"
                fullWidth
                value={formik.values.pin}
                onChange={(e) => {
                  const numericValue = e.target.value.replace(/\D/g, '')
                  formik.setFieldValue('pin', numericValue)
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.pin && Boolean(formik.errors.pin)}
                helperText={formik.touched.pin && formik.errors.pin}
                inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
              />
            </MUIGrid>
            <MUIGrid size={{ xs: 12 }} mb={2} position="relative">
              <MUIPasswordField
                name="ConfirmPin"
                placeholder="Confirm new pin"
                fullWidth
                value={formik.values.ConfirmPin}
                onChange={(e) => {
                  const numericValue = e.target.value.replace(/\D/g, '')
                  formik.setFieldValue('ConfirmPin', numericValue)
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.ConfirmPin && Boolean(formik.errors.ConfirmPin)}
                helperText={formik.touched.ConfirmPin && formik.errors.ConfirmPin}
                inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
              />
            </MUIGrid>
          </MUIGrid>
          <MUIStack display="flex" flexDirection="row" gap={2} mt={2}>
            <MUIButton
              sx={{
                borderRadius: 2.5,
                minWidth: 140,
                width: '100%',
                backgroundColor: theme.palette.danger?.main,
              }}
              variant="contained"
              type="button"
              onClick={handleCloseResetPinModel}
              disabled={isSubmitting}
            >
              Cancel
            </MUIButton>
            <MUIButton
              sx={{
                borderRadius: 2.5,
                minWidth: 140,
                backgroundColor: formik.isValid && formik.dirty ? 'primary.main' : 'gray',
                width: '100%',
              }}
              variant="contained"
              type="submit"
              disabled={!formik.isValid || !formik.dirty || isSubmitting}
            >
              {isSubmitting ? 'Resetting...' : 'Reset'}
            </MUIButton>
          </MUIStack>
        </form>
      </Box>
    </Modal>
  )
}

export default ResetPinModel
