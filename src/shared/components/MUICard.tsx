import React from 'react'
import MUIStack from './MUIStack'

interface CardProps {
  children: any
}

const MUICard: React.FC<CardProps> = ({ children }) => {
  return (
    <>
      <MUIStack
        sx={{
          backgroundColor: 'white.main',
          p: { md: 4, xs: 2 },
          borderRadius: 6,
          flex: 1,
          transition: 'all 225ms cubic-bezier(0, 0, 0.2, 1)',
          textAlign: 'left',
          minHeight: { md: 'calc(100vh - 40px)', xs: 'calc(100vh - 60px)' },
          width: '100%',
        }}
      >
        {children}
      </MUIStack>
    </>
  )
}

export default MUICard
