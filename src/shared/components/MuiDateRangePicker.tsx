import { useEffect } from 'react'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { DateRangePicker } from '@mui/x-date-pickers-pro'

interface IMuiDateRangePicker {
  value: [Date | null, Date | null]
  onChange?: (args: [Date | null, Date | null]) => void
  onAccept?: (args: [Date | null, Date | null]) => void
  format?: string
}

const MUIDateRangePicker: React.FC<IMuiDateRangePicker> = ({ value, onChange, format, onAccept, ...props }) => {
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const licenseKeyDiv: any = document.querySelector('div[style*="position: absolute"][style*="z-index: 100000"]')
      if (licenseKeyDiv) {
        licenseKeyDiv.style.display = 'none'
      }
    })

    // Target the entire document or a specific container if needed
    observer.observe(document.body, {
      childList: true, // Observe added/removed child elements
      subtree: true, // Observe all descendants (not just direct children)
    })

    // Clean up observer when component unmounts
    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateRangePicker
        {...props}
        value={value}
        // onChange={(e) => onChange(e as [Date | null, Date | null])}
        format={format}
        defaultValue={[new Date(), new Date()]}
        onAccept={(e) => onAccept?.(e as [Date | null, Date | null])}
        localeText={{ start: 'DD/MM/YYYY', end: 'DD/MM/YYYY' }}
      />
    </LocalizationProvider>
  )
}

export default MUIDateRangePicker
