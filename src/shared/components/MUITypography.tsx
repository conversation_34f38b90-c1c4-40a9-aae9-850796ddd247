import React, { ReactNode } from 'react'
import { Typography, TypographyProps } from '@mui/material'

// Define the props interface extending MUI TypographyProps
interface MUITypographyProps extends TypographyProps {
  children: ReactNode
}

const MUITypography: React.FC<MUITypographyProps> = ({ children, ...props }) => {
  return <Typography {...props}>{children}</Typography>
}

export default MUITypography
