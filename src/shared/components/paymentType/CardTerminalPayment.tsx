import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import AmountModal from '../AmountModal'
import M<PERSON>Box from '../MUIBox'
import M<PERSON><PERSON>utton from '../MUIButton'
import MUIDialog from '../MUIDialog'
import MUIListItem from '../MUIListItem'
import { useCancelPayment } from '@/shared/hooks/apis/cancelPayment'
import theme from '@/styles/theme'

interface CardAmountProps {
  totalAmount: string
  email?: string
  onPay?: (amount: string, setIsLoadingOrToken: React.Dispatch<React.SetStateAction<boolean>>) => Promise<void>
  isVoucherPurchase?: boolean
  voucherPurchaseWarning?: string
  multiCheckoutApplied?: boolean
  onPaymentComplete?: () => void
  onPaymentCancel?: () => void
}

const CardPaymentSection: React.FC<CardAmountProps> = ({
  onPay,
  totalAmount,
  isVoucherPurchase = false,
  voucherPurchaseWarning,
  multiCheckoutApplied,
  onPaymentCancel,
}) => {
  const [cardDialog, setCardDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [amount, setAmount] = useState(totalAmount)
  const { mutate: cancelPayment } = useCancelPayment()

  useEffect(() => {
    setAmount(totalAmount)
  }, [totalAmount])

  const handleCardChange = (event: any) => {
    if (event.error) {
      console.error(event.error.message)
    }
  }

  const handleCancelPayment = async () => {
    try {
      await cancelPayment()
      setIsLoading(false)
      onPaymentCancel?.() // Call the cancellation handler
      toast.success('Payment cancelled successfully')
    } catch (error) {
      toast.error('Failed to cancel payment. Please try again.')
    }
  }

  const handleAmountFinalize = async (value: string) => {
    setAmount(value)
    setIsLoading(true)

    try {
      onPay && (await onPay(value, setIsLoading))
      // Note: Sales summary will be handled by the polling logic in parent component
    } catch (error) {
      setIsLoading(false)
      toast.error('Payment failed')
    }
  }
  return (
    <>
      <MUIListItem
        onClick={() => {
          if (multiCheckoutApplied) {
            toast.error('Can not process payment with multiple checkout')
          } else if (isVoucherPurchase) {
            toast.error('Please remove Gift Card to use Card Terminal')
          } else {
            setCardDialog(true)
          }
        }}
      >
        <MUIBox className="card">
          <i className="ri-bank-card-line"></i>Card Terminal
        </MUIBox>
      </MUIListItem>

      {cardDialog && (
        <AmountModal
          open={cardDialog}
          onPay={(amount) => {
            setCardDialog(false)
            handleAmountFinalize(amount)
          }}
          amount={amount}
          onClose={() => setCardDialog(false)}
          isVoucherPurchase={isVoucherPurchase}
          voucherPurchaseWarning={voucherPurchaseWarning}
          title="Add card amount"
          maxAmount={amount}
        />
      )}

      {isLoading && (
        <MUIDialog
          dialogContentSX={{ pt: 2, textAlign: 'center' }}
          maxWidth="xs"
          open={isLoading}
          title="Processing Payment"
          hideClose={true}
        >
          <style
            dangerouslySetInnerHTML={{
              __html: `
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
              .spinner {
                animation: spin 1s linear infinite;
                display: inline-block;
              }
            `,
            }}
          />
          <MUIBox sx={{ p: 3 }}>
            <div style={{ marginBottom: '20px' }}>
              <i className="ri-loader-4-line spinner" style={{ fontSize: '40px' }}></i>
            </div>
            <div>Please wait while we process your payment...</div>
            <MUIButton
              rounded
              sx={{
                mt: 2,
                minWidth: 120,
                backgroundColor: theme.palette.error.main,
              }}
              variant="contained"
              onClick={handleCancelPayment}
            >
              Cancel Payment
            </MUIButton>
          </MUIBox>
        </MUIDialog>
      )}
    </>
  )
}
const CardPayment: React.FC<CardAmountProps> = (props) => {
  return <CardPaymentSection {...props} />
}

export default CardPayment
