import { useEffect, useMemo, useState } from 'react'
import { CardElement, Elements, useElements, useStripe } from '@stripe/react-stripe-js'
import { loadStripe, Token } from '@stripe/stripe-js'
import AmountModal from '../AmountModal'
import <PERSON><PERSON><PERSON><PERSON> from '../MUIBox'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../MUIButton'
import MUI<PERSON>ialog from '../MUIDialog'
import MUIListItem from '../MUIListItem'
import styles from '@/features/appointments/components/addClientDetail/stripeCard/StripeCard.module.scss'
import TermsService from '@/features/appointments/components/addClientDetail/stripeCard/TermsService'
import { useGetStripPublicKey } from '@/shared/hooks/apis/getStripePublicKey'

interface CardAmountProps {
  totalAmount: string
  email?: string
  onPay?: (amount: string, tokenResponse: Token, createStripeToken: any) => void
  isVoucherPurchase?: boolean
  voucherPurchaseWarning?: string
  mergedCheckout?: string | any
  noShowOpen?: boolean
  setNoShowOpen?: React.Dispatch<React.SetStateAction<boolean>>
  onClose?: () => void
}

const NoShowCardPaymentSection: React.FC<CardAmountProps> = ({
  onPay,
  email,
  totalAmount,
  isVoucherPurchase = false,
  voucherPurchaseWarning,
  mergedCheckout,
  onClose,
  setNoShowOpen,
}) => {
  const [cardDialog, setCardDialog] = useState(true)
  const [isStripeCard, setIsStripeCard] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [termsAccepted, setTermsAccepted] = useState(false)

  const stripe = useStripe()
  const elements = useElements()

  const [isCardComplete, setIsCardComplete] = useState(false)
  const isFormValid = stripe && isCardComplete && termsAccepted && !isLoading

  const [amount, setAmount] = useState(totalAmount)
  useEffect(() => {
    setAmount(totalAmount)
  }, [totalAmount])

  const handleCardChange = (event: any) => {
    if (event.error) {
      console.error(event.error.message)
    }
    setIsCardComplete(event.complete || false)
  }

  const handleTermsAccepted = (accepted: boolean) => {
    setTermsAccepted(accepted)
  }

  const handleAmountFinalize = (value: string) => {
    setAmount(value)
    setIsStripeCard(true)
  }

  const createStripeToken = async () => {
    if (!stripe || !elements) {
      throw new Error('Stripe not initialized')
    }

    const cardElement = elements.getElement(CardElement)
    if (!cardElement) {
      throw new Error('Card element not found')
    }

    const { token, error } = await stripe.createToken(cardElement, {
      name: email,
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!token) {
      throw new Error('Failed to create token')
    }
    return token
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isLoading) return

    try {
      setIsLoading(true)
      const tokenResponse = await createStripeToken()

      onPay && (await onPay(amount, tokenResponse, createStripeToken))
    } catch (err) {
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (!cardDialog && !isStripeCard && setNoShowOpen) {
      setNoShowOpen(false)
    }
  }, [cardDialog, isStripeCard])

  return (
    <>
      {/* <MUIListItem onClick={() => setCardDialog(true)}>
        <MUIBox className="card">
          <i className="ri-bank-card-line"></i>Card
        </MUIBox>
      </MUIListItem> */}

      {/* {cardDialog && ( */}
      <AmountModal
        open={cardDialog}
        onPay={(amount) => {
          setCardDialog(false)
          handleAmountFinalize(amount)
        }}
        amount={amount}
        onClose={() => setCardDialog(false)}
        isVoucherPurchase={isVoucherPurchase}
        voucherPurchaseWarning={voucherPurchaseWarning}
        title="Add card amount"
        multicheckoutWarning={mergedCheckout?.length > 1 ? 'Can not split amount in multi checkout' : undefined}
        maxAmount={amount}
      />
      {/* )} */}

      {isStripeCard && (
        <MUIDialog
          dialogContentSX={{ pt: 0 }}
          maxWidth="xs"
          open={isStripeCard}
          handleClose={() => setIsStripeCard(false)}
          title="Pay"
        >
          <form onSubmit={(e) => handleSubmit(e)} className={styles.formContainer}>
            <div className={styles.cardSection}>
              <label className={styles.cardLabel}>Card Details</label>
              <div className={styles.cardInputContainer}>
                <CardElement
                  options={{
                    style: {
                      base: {
                        fontSize: '16px',
                        color: '#424770',
                        '::placeholder': {
                          color: '#aab7c4',
                        },
                      },
                      invalid: {
                        color: '#9e2146',
                      },
                    },
                    hidePostalCode: true,
                  }}
                  onChange={handleCardChange}
                />
              </div>
              <TermsService onTermsAccepted={handleTermsAccepted} />
            </div>

            <MUIButton type="submit" variant="outlined" disabled={!isFormValid} className={styles.button}>
              {isLoading ? `Processing` : `Pay now €${amount}`}
            </MUIButton>
          </form>
        </MUIDialog>
      )}
    </>
  )
}

export { NoShowCardPaymentSection }

const NoShowCardPayment: React.FC<CardAmountProps> = (props) => {
  const { data } = useGetStripPublicKey()

  const stripePromise = useMemo(() => {
    if (!data) return null
    return loadStripe(data)
  }, [data])

  if (!stripePromise) return null
  return (
    <Elements stripe={stripePromise}>
      <NoShowCardPaymentSection {...props} />
    </Elements>
  )
}

export default NoShowCardPayment
