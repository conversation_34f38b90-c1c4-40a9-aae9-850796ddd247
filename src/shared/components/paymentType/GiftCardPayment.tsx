import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import AmountModal from '../AmountModal'
import M<PERSON>Box from '../MUIBox'
import MUIListItem from '../MUIListItem'
import GiftCardDialog from '@/features/appointments/components/addClientDetail/GiftCardDialog'
import { getAdjustedAppointmentAmounts } from '@/features/appointments/components/addClientDetail/helper'
import { ISelectedServices } from '@/redux/slice/calenderSlice/interface'
import useCalendarSliceAction from '@/redux/slice/calenderSlice/useCalenderSliceAction'
import { useGetAllVouchers } from '@/shared/hooks/apis/getAllVouchers'
import { IVoucherCheckoutPayload } from '@/shared/hooks/apis/voucherCheckout/apis'
import useVoucherCheckout from '@/shared/hooks/apis/voucherCheckout/voucherCheckout'

interface GiftCardProps {
  onClose: () => void
  amount: string
  onPay?: (value: string) => void
  onChange?: (value: string) => void
  isLoading?: boolean
  selectedClientId?: number
  isVoucherPurchase?: boolean
  successCallback?: () => void
}

const GiftCardPayment: React.FC<GiftCardProps> = ({
  amount,
  onChange,
  onPay,
  isLoading,
  isVoucherPurchase = false,
  successCallback,
}) => {
  const { selectedSlot: slot, checkoutServices, selectedGiftVoucher } = useCalendarSliceAction()
  const [giftCardDialogOpen, setGiftCardDialogOpen] = useState(false)
  const [amountModalOpen, setAmountModalOpen] = useState(false)
  const [voucherAmount, setVoucherAmount] = useState<string>('')
  const [mergedCheckout, setMergedCheckout] = useState<ISelectedServices[]>([])
  const { execute: voucherCheckout } = useVoucherCheckout()
  const { mutate: allVoucher } = useGetAllVouchers()

  useEffect(() => {
    const safeSlot = Array.isArray(slot) ? slot : slot ? [slot] : []
    setMergedCheckout([...safeSlot, ...checkoutServices])
  }, [slot, checkoutServices])

  const handleVoucherApplied = (balance: string) => {
    setVoucherAmount(balance)
    onChange?.(balance)
    setGiftCardDialogOpen(false)
    setAmountModalOpen(true)
  }

  const handlePayment = async (amt: string) => {
    try {
      // Get adjusted appointment amounts based on gift amount
      const adjustedAppointments = getAdjustedAppointmentAmounts(mergedCheckout, amt)

      // Filter out appointments that have gift amount applied
      const appointmentsToProcess = adjustedAppointments.filter((app: any) => app.giftAmountApplied > 0)

      if (appointmentsToProcess.length === 0) {
        toast.error('No appointments eligible for gift card payment')
        return
      }

      const voucherCodes: string[] = []
      if (selectedGiftVoucher && selectedGiftVoucher.uniqueVoucherCode) {
        voucherCodes.push(selectedGiftVoucher.uniqueVoucherCode)
      }

      if (voucherCodes.length === 0) {
        toast.error('No voucher selected')
        return
      }

      // Process each appointment individually with its own API call
      for (const appointment of appointmentsToProcess) {
        // Get group appointment ID if available
        const groupAppointmentId = appointment.groupAppointmentId ? String(appointment.groupAppointmentId) : undefined
        const groupAppointmentIds: string[] = groupAppointmentId ? [groupAppointmentId] : []

        // Amount to apply for this specific appointment
        const appointmentAmount = appointment.giftAmountApplied

        const payload: IVoucherCheckoutPayload = {
          voucherCodes,
          groupAppointmentIds,
          serviceAmount: appointmentAmount,
        }

        // Call API for this specific appointment
        await voucherCheckout(payload)
        toast.success('Voucher payment successful')
      }

      onPay?.(amt)
      setAmountModalOpen(false)

      allVoucher()
      await successCallback?.()
    } catch (error: any) {
      toast.error(error.response.data.message)
    }
  }

  return (
    <>
      <MUIListItem
        onClick={() =>
          isVoucherPurchase ? toast.error('Please remove Gift Card to use Gift card') : setGiftCardDialogOpen(true)
        }
      >
        <MUIBox className="gift-card">
          <i className="ri-gift-line"></i>Gift card
        </MUIBox>
      </MUIListItem>

      {giftCardDialogOpen && (
        <GiftCardDialog
          open={giftCardDialogOpen}
          onClose={() => setGiftCardDialogOpen(false)}
          onVoucherApply={handleVoucherApplied}
        />
      )}

      {amountModalOpen && (
        <AmountModal
          open={amountModalOpen}
          onPay={handlePayment}
          amount={voucherAmount}
          onChange={onChange}
          onClose={() => setAmountModalOpen(false)}
          isLoading={isLoading}
          title={'Use voucher amount'}
          maxAmount={amount}
        />
      )}
    </>
  )
}

export default GiftCardPayment
