import { useState } from 'react'
import AmountModal from '../AmountModal'
import MUIBox from '../MUIBox'
import M<PERSON><PERSON>istItem from '../MUIListItem'

interface CashAmountProps {
  onClose: () => void
  amount: string
  onPay?: (value: string) => void
  onChange?: (value: string) => void
  isLoading?: boolean
  isVoucherPurchase?: number
  voucherPurchaseWarning?: string
  mergedCheckout?: string | any
  maxAmount?: string
}

const CashPayment: React.FC<CashAmountProps> = ({
  amount,
  maxAmount,
  onChange,
  onPay,
  isLoading,
  isVoucherPurchase,
  voucherPurchaseWarning,
  onClose,
  mergedCheckout,
}) => {
  const [cashDialog, setCashDialog] = useState(false)
  return (
    <>
      <MUIListItem onClick={() => setCashDialog(true)}>
        <MUIBox className="cash">
          <i className="ri-money-euro-circle-line"></i>Cash
        </MUIBox>
      </MUIListItem>

      {cashDialog && (
        <AmountModal
          open={cashDialog}
          onPay={(amount) => {
            onPay?.(amount)
          }}
          amount={amount}
          onChange={onChange}
          onClose={() => {
            setCashDialog(false)
            onClose?.()
          }}
          isLoading={isLoading}
          isVoucherPurchase={isVoucherPurchase}
          voucherPurchaseWarning={voucherPurchaseWarning}
          multicheckoutWarning={mergedCheckout.length > 1 ? 'Can not split amount in multi checkout' : undefined}
          maxAmount={maxAmount}
        />
      )}
    </>
  )
}

export default CashPayment
