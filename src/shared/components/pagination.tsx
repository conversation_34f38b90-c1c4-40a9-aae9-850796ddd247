import React from 'react'
import { Stack, Typography, Select, MenuItem, Pagination } from '@mui/material'

type TablePaginationProps = {
  pageIndex: number
  pageSize: number
  pageCount: number
  onPageChange: (pageIndex: number) => void
  onPageSizeChange: (pageSize: number) => void
}

const TablePagination: React.FC<TablePaginationProps> = ({
  pageIndex,
  pageSize,
  pageCount,
  onPageChange,
  onPageSizeChange,
}) => {
  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      flexWrap="wrap"
      px={2}
      py={1}
      sx={{
        position: 'sticky',
        bottom: 0,
        backgroundColor: '#fff',
        borderTop: '1px solid #e0e0e0',
        zIndex: 2,
      }}
    >
      {/* Rows per page selector */}
      <Stack direction="row" alignItems="center" spacing={1}>
        <Typography variant="body2">Rows per page:</Typography>
        <Select
          size="small"
          value={pageSize}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          sx={{ minWidth: 80 }}
        >
          {[10, 25, 50, 100].map((size) => (
            <MenuItem key={size} value={size}>
              {size}
            </MenuItem>
          ))}
        </Select>
      </Stack>

      {/* Pagination */}
      <Pagination
        count={pageCount}
        page={pageIndex + 1}
        onChange={(_, page) => onPageChange(page - 1)}
        shape="rounded"
        size="small"
        siblingCount={1}
        boundaryCount={1}
        color="primary"
      />
    </Stack>
  )
}

export default TablePagination
