import React, { ReactNode } from 'react'
import Scrollbars, { ScrollbarProps } from 'react-custom-scrollbars-2' // Ensure proper types are imported
import MUIStack from './MUIStack' // Adjust import path as needed

interface CustomScrollbarProps extends ScrollbarProps {
  sx?: Record<string, any> // Optional sx prop for custom styles
  children?: ReactNode // React children
}

const CustomScrollbar: React.FC<CustomScrollbarProps> = ({ sx, children, ...props }) => {
  return (
    <Scrollbars
      autoHide
      renderTrackVertical={(trackProps) => <MUIStack right={0} bottom={0} top={0} sx={{ zIndex: 9 }} {...trackProps} />}
      renderThumbVertical={(thumbProps) => (
        <MUIStack backgroundColor="lightGray.main" borderRadius={2} sx={sx} {...thumbProps} />
      )}
      {...props}
    >
      {children}
    </Scrollbars>
  )
}

export default CustomScrollbar
