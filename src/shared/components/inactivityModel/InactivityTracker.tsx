import React, { useEffect, useState, useRef } from 'react'
import { useLocation } from 'react-router-dom'
import InactivityModel from './InactivityModel'
import useStaffLoginDetailsSliceAction from '@/redux/slice/staffLoginSlice/useStaffLoginDetailsSliceAction'

// const INACTIVITY_TIMEOUT = 30000 // 30 seconds for testing (originally 15 * 60 * 1000 for 15 minutes)
const INACTIVITY_TIMEOUT = 900000 // 15 minutes

const InactivityTracker: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [resetFormTrigger, setResetFormTrigger] = useState(0)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const userActivityRef = useRef(false)
  const manualCloseRef = useRef(false)
  const location = useLocation()
  const previousLocationRef = useRef(location.pathname)
  const { staffLoginDetails, setStaffLoginDetails } = useStaffLoginDetailsSliceAction()

  // Helper function to check if PIN is empty (works for both array and string)
  const isPinEmpty = (pin: any) => {
    if (Array.isArray(pin)) {
      return pin.length === 0
    }
    return !pin
  }

  useEffect(() => {
    if (staffLoginDetails === null) {
      setIsModalOpen(true)
    }
  }, [staffLoginDetails])
  // Function to check if modal should be shown
  const shouldShowModal = () => {
    if (location.pathname === '/reset-pin') {
      return false
    }
    return isPinEmpty(staffLoginDetails)
  }

  // Function to reset the inactivity timer
  const resetTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current)
    }

    if (manualCloseRef.current || location.pathname === '/reset-pin') {
      return
    }

    timerRef.current = setTimeout(() => {
      if (userActivityRef.current || !userActivityRef.current) {
        if (!isPinEmpty(staffLoginDetails)) {
          setStaffLoginDetails(Array.isArray(staffLoginDetails) ? [] : null)
          setIsModalOpen(true)
        } else if (isModalOpen) {
          setResetFormTrigger((prev) => prev + 1)
        } else {
          setIsModalOpen(true)
        }
        userActivityRef.current = false
      }
    }, INACTIVITY_TIMEOUT)
  }

  // Function to handle user activity
  const handleUserActivity = () => {
    userActivityRef.current = true

    if (location.pathname === '/reset-pin') {
      return
    }

    // Always reset timer on user activity
    resetTimer()
  }

  // Function to handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false)
    manualCloseRef.current = true

    setTimeout(() => {
      manualCloseRef.current = false
      userActivityRef.current = true
      resetTimer()
    }, 1000)
  }

  // Effect to handle page refresh and modal visibility
  useEffect(() => {
    setStaffLoginDetails(Array.isArray(staffLoginDetails) ? [] : null)

    const wasOnResetPinRoute = previousLocationRef.current === '/reset-pin'
    const isNowOnDifferentRoute = location.pathname !== '/reset-pin'
    const comingFromResetPin = wasOnResetPinRoute && isNowOnDifferentRoute

    if (shouldShowModal()) {
      if (comingFromResetPin) {
        setTimeout(() => {
          if (shouldShowModal()) {
            setIsModalOpen(true)
          }
        }, 200)
      } else {
        setIsModalOpen(true)
      }
    } else if (!isPinEmpty(staffLoginDetails) && isModalOpen) {
      setIsModalOpen(false)
    }

    previousLocationRef.current = location.pathname
  }, [])

  // Effect to set up event listeners and timers
  useEffect(() => {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    userActivityRef.current = true

    activityEvents.forEach((event) => {
      window.addEventListener(event, handleUserActivity)
    })

    if (location.pathname !== '/reset-pin' && !isPinEmpty(staffLoginDetails)) {
      resetTimer()
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
      activityEvents.forEach((event) => {
        window.removeEventListener(event, handleUserActivity)
      })
    }
  }, [location.pathname, staffLoginDetails, isModalOpen])

  return (
    <>
      {children}
      <InactivityModel
        open={isModalOpen && location.pathname !== '/reset-pin'}
        onClose={handleCloseModal}
        setIsModalOpen={setIsModalOpen}
        resetFormTrigger={resetFormTrigger}
      />
    </>
  )
}

export default InactivityTracker
