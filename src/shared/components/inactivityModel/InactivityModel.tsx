import { useState, useEffect } from 'react'
import { Box, Modal } from '@mui/material'
import { useFormik } from 'formik'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import * as Yup from 'yup'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../MUIButton'
import M<PERSON><PERSON><PERSON> from '../MUIGrid'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../MUIPasswordField'
import M<PERSON>Select from '../MUISelect'
import MUIStack from '../MUIStack'
import MUITypography from '../MUITypography'
import ForgotPinModel from '../userPinModel/ForgotPinModel'
import useStaffLoginDetailsSliceAction from '@/redux/slice/staffLoginSlice/useStaffLoginDetailsSliceAction'
import { getFirstAccessibleRoute, usePermissions } from '@/shared/helper'
import { useGetAllStaff } from '@/shared/hooks/apis/getAllStaff'
import { useGetPermissions } from '@/shared/hooks/apis/permissions/getPermissions'
import { IVerifyStaffMemberPinPayload } from '@/shared/hooks/apis/verifyStaffMemberPin/apis'
import useVerifyStaffMemberPin from '@/shared/hooks/apis/verifyStaffMemberPin/verifyStaffMemberPin'

interface InactivityModalProps {
  open: boolean
  onClose: () => void
  setIsModalOpen: (open: boolean) => void
  resetFormTrigger: number
}

type OptionType = {
  value: number
  label: string
}

const validationSchema = Yup.object({
  staffId: Yup.string().required('Please select a staff member'),
  pin: Yup.string()
    .required('PIN is required')
    .matches(/^\d{6}$/, 'PIN must be exactly 6 digits'),
})

const InactivityModel: React.FC<InactivityModalProps> = ({ open, onClose, setIsModalOpen, resetFormTrigger }) => {
  const { data: staff } = useGetAllStaff()
  const { execute: verifyPin } = useVerifyStaffMemberPin()
  const [staffOptions, setStaffOptions] = useState<OptionType[]>([])
  const [forgotModelOpen, setForgotModelOpen] = useState<boolean>(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { setStaffLoginDetails } = useStaffLoginDetailsSliceAction()
  const { data: permission } = useGetPermissions()
  const navigate = useNavigate()

  const staffSelectMenuProps = {
    PaperProps: {
      style: {
        maxHeight: 250,
        overflow: 'auto',
      },
    },
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'left',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'left',
    },
    getContentAnchorEl: null,
  }

  useEffect(() => {
    if (staff && staff.length > 0) {
      const options = staff.map((staffMember) => ({
        value: staffMember?.employee_id,
        label: `${staffMember?.firstName} ${staffMember?.lastName}`,
      }))
      setStaffOptions(options)
    }
  }, [staff])

  useEffect(() => {
    if (open) {
      setForgotModelOpen(false)
    }
    formik.resetForm()
  }, [open])

  // Add this effect to reset form when resetFormTrigger changes
  useEffect(() => {
    if (resetFormTrigger > 0) {
      formik.resetForm()
    }
  }, [resetFormTrigger])

  const formik = useFormik({
    initialValues: {
      staffId: '',
      pin: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      const selectedStaff = staff?.find((s) => s.employee_id === Number(values.staffId))

      if (!selectedStaff) {
        toast.error('Selected staff member not found')
        return
      }

      try {
        setIsSubmitting(true)

        const payload: IVerifyStaffMemberPinPayload = {
          id: values.staffId,
          pin: values.pin,
        }

        const response = await verifyPin(payload)
        setStaffLoginDetails(selectedStaff)

        setTimeout(() => {
          const userRole = selectedStaff?.role
          const firstAccessibleRoute = getFirstAccessibleRoute(userRole, permission as any)

          if (firstAccessibleRoute && firstAccessibleRoute !== '/unauthorized') {
            navigate(firstAccessibleRoute)
            formik.resetForm()
            onClose()
          } else {
            toast.error('No accessible routes found. Please contact administrator.')
          }
        }, 0)
      } catch (error: any) {
        toast.error(error?.response?.data?.message || 'Login failed')
      } finally {
        setIsSubmitting(false)
      }
    },
  })

  const handleForgotPin = () => {
    onClose()
    setForgotModelOpen(true)
  }
  const handleCancel = () => {
    setForgotModelOpen(false)
    setIsModalOpen(true)
  }

  return (
    <>
      <Modal
        open={open}
        disableEscapeKeyDown={true}
        disableAutoFocus={true}
        disableEnforceFocus={true}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
        }}
      >
        <Box
          sx={{
            width: 400,
            outline: 'none',
            bgcolor: 'background.paper',
            borderRadius: 4,
            boxShadow: 24,
            p: 3,
            m: '0px 20px',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <form onSubmit={formik.handleSubmit}>
            <MUITypography variant="h5" fontWeight={600}>
              Welcome Back!
            </MUITypography>
            <MUITypography variant="body2">Log in to book and manage your appointments.</MUITypography>
            <MUIGrid container mt={2}>
              <MUIGrid size={{ xs: 12 }} mb={2}>
                <MUISelect
                  name="staffId"
                  value={formik.values.staffId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  options={staffOptions}
                  placeholder="Select staff member"
                  error={formik.touched.staffId && Boolean(formik.errors.staffId)}
                  helperText={formik.touched.staffId && formik.errors.staffId}
                  MenuProps={staffSelectMenuProps}
                />
              </MUIGrid>
              <MUIGrid size={{ xs: 12 }} mb={2} position="relative">
                <MUIPasswordField
                  name="pin"
                  placeholder="Your pin"
                  fullWidth
                  value={formik.values.pin}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.pin && Boolean(formik.errors.pin)}
                  helperText={formik.touched.pin && formik.errors.pin}
                />
              </MUIGrid>
            </MUIGrid>
            <Box display="flex" alignItems="center" justifyContent="flex-end">
              <MUITypography variant="body2" sx={{ cursor: 'pointer' }} onClick={handleForgotPin}>
                Forgot pin?
              </MUITypography>
            </Box>
            <MUIStack mt={2}>
              <MUIButton
                sx={{
                  borderRadius: 2.5,
                  minWidth: 140,
                  backgroundColor: formik.isValid && formik.dirty ? 'primary.main' : 'gray',
                  '@media (max-width:500px)': {
                    width: '100%',
                  },
                }}
                variant="contained"
                type="submit"
                disabled={!formik.isValid || !formik.dirty || isSubmitting}
              >
                {isSubmitting ? 'Logging in...' : 'Log in'}
              </MUIButton>
            </MUIStack>
          </form>
        </Box>
      </Modal>
      <ForgotPinModel open={forgotModelOpen} setForgotModelOpen={setForgotModelOpen} handleCancel={handleCancel} />
    </>
  )
}

export default InactivityModel
