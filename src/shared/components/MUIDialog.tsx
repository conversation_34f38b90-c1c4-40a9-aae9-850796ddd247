import React, { ReactNode } from 'react'
import CloseIcon from '@mui/icons-material/Close'
import { Dialog, DialogContent, DialogTitle, IconButton, DialogProps, Box } from '@mui/material'
import CustomScrollbar from './CustomScrollbar'
import { MUIStyled } from './MUIStyled'
import theme from '@/styles/theme'

// Define the props for MUIDialog, extending DialogProps from MUI
interface MUIDialogProps extends DialogProps {
  title?: any
  open: boolean
  hideClose?: boolean
  autoHeightMax?: any
  handleClose?: () => void
  children?: ReactNode
  dialogContentSX?: any
}

const StyledDialog = MUIStyled(Dialog)(({ theme }) => ({
  '& .MuiBackdrop-root': {
    backgroundColor: `${theme.palette.black?.dark}33`,
  },
  '& .MuiPaper-root': {
    borderRadius: 20,
  },
  [theme.breakpoints.down('xsm')]: {
    alignItems: 'flex-end',
    '& .MuiPaper-root': {
      bottom: 0,
      position: 'absolute',
      margin: 0,
      maxWidth: '100%',
      width: '100%',
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
  },
}))

const MUIDialog: React.FC<MUIDialogProps> = ({
  title,
  open,
  hideClose,
  children,
  autoHeightMax,
  handleClose,
  dialogContentSX,
  ...props
}) => {
  return (
    <StyledDialog open={open} onClose={handleClose} fullWidth {...props}>
      {title && (
        <DialogTitle
          sx={{ fontWeight: 600, '& i': { fontWeight: 'normal', mr: 1, color: theme.palette.lightGray?.light200 } }}
        >
          {title}
        </DialogTitle>
      )}
      {!hideClose && (
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: 'absolute',
            right: 10,
            top: 12,
            color: theme.palette.gray?.light,
          })}
        >
          <CloseIcon />
        </IconButton>
      )}
      <DialogContent sx={dialogContentSX}>
        <Box p={0.1}>
          <CustomScrollbar
            autoHeight
            autoHeightMin={100}
            autoHeightMax={autoHeightMax ? autoHeightMax : 'calc(100vh - 170px)'}
          >
            {children}
          </CustomScrollbar>
        </Box>
      </DialogContent>
    </StyledDialog>
  )
}

export default MUIDialog
