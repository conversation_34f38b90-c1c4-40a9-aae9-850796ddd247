import { TypographyProps } from '@mui/material'
import { styled, useTheme } from '@mui/material/styles'
import MUITypography from '@/shared/components/MUITypography'

type Props = {
  percentage: string | number | null | undefined
} & TypographyProps

const Percentage = styled(MUITypography)<TypographyProps>(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  minWidth: 60,
  marginBottom: theme.spacing(0.5),
  marginLeft: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  padding: `${theme.spacing(0.5)} ${theme.spacing(1)}`,
}))

const PercentageChangeBadge = ({ percentage, ...rest }: Props) => {
  const theme = useTheme()

  // Coerce string to number safely
  const numericPercentage = Number(typeof percentage === 'string' ? percentage.replace('%', '').trim() : percentage)

  const getArrowDirection = (value: number) => {
    if (value === 0) return 'up-down'
    return value < 0 ? 'down' : 'up'
  }

  const getPercentageStyles = (value: number) => {
    if (value === 0) {
      return {
        backgroundColor: theme.palette.grey[200],
        color: theme.palette.text.primary,
      }
    }
    if (value < 0) {
      return {
        backgroundColor: theme.palette.danger?.light || '#fdecea',
        color: theme.palette.danger?.dark || '#d32f2f',
      }
    }
    return {
      backgroundColor: theme.palette.success.light,
      color: theme.palette.success.main,
    }
  }

  function formatPercentage(value: number): string {
    if (isNaN(value)) return '0%'
    return `${value}%`
  }

  return (
    <Percentage
      variant="body2"
      sx={{
        ...getPercentageStyles(numericPercentage),
        width: 'fit-content',
        minWidth: 32, // optional, keeps it readable
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        paddingInline: 1,
        whiteSpace: 'nowrap',
      }}
      {...rest}
    >
      <i className={`ri-arrow-${getArrowDirection(numericPercentage)}-line`} style={{ marginRight: 4 }}></i>
      {formatPercentage(numericPercentage)}
    </Percentage>
  )
}

export default PercentageChangeBadge
