import React, { ReactNode } from 'react'
import { FormLabel, FormLabelProps } from '@mui/material'
import { MUIStyled } from './MUIStyled'

// StyledFormLabel with custom styles
const StyledFormLabel = MUIStyled(FormLabel)(({ theme }) => ({
  fontSize: 14,
  fontWeight: 500,
  lineHeight: '1.5',
  color: theme.palette.gray?.light,
  marginBottom: 5,
  display: 'flex',
}))

// Define the prop types for MUIFormLabel
interface MUIFormLabelProps extends FormLabelProps {
  children: ReactNode
}

const MUIFormLabel: React.FC<MUIFormLabelProps> = ({ children, ...props }) => {
  return <StyledFormLabel {...props}>{children}</StyledFormLabel>
}

export default MUIFormLabel
