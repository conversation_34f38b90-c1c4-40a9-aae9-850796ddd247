import React from 'react'
import { Tooltip } from '@mui/material'

interface MUITooltipProps {
  title: string
  children: React.ReactNode
  placement?: 'top' | 'bottom' | 'left' | 'right'
}

const MUITooltip: React.FC<MUITooltipProps> = ({ title, children, placement = 'top' }) => {
  return (
    <Tooltip title={title} arrow placement={placement}>
      <span>{children}</span>
    </Tooltip>
  )
}

export default MUITooltip
