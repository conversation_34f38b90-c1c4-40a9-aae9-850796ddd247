import React, { ReactNode } from 'react'
import { Menu, MenuProps } from '@mui/material'
import theme from '@/styles/theme'

interface MUIMenuProps extends MenuProps {
  paper?: any
  isMouseDown?: boolean
  children: ReactNode
}

const MUIMenu: React.FC<MUIMenuProps> = ({ paper, isMouseDown = false, children, ...props }) => {
  return (
    <Menu
      {...props}
      slotProps={{
        paper: {
          onMouseDown: (event: React.MouseEvent) => {
            isMouseDown && event.stopPropagation()
          },
          sx: {
            borderRadius: 3,
            boxShadow: '0px 10px 16px -3px #14151A14',
            minWidth: 180,
            border: `1px solid ${theme.palette.gray?.main}`,
            '& .MuiList-root': {
              p: 1,
              '& .MuiMenuItem-root': {
                borderRadius: 2,
                fontSize: 14,
                padding: '6px 8px',
                minHeight: 'auto',
              },
            },
            '& i': {
              marginRight: 1,
              fontSize: 18,
              color: theme.palette.lightGray?.light200,
            },
            ...paper, // Allow for custom styling of paper prop
          },
        },
      }}
    >
      {children}
    </Menu>
  )
}

export default MUIMenu
