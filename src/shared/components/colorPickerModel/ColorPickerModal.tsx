import React, { useState, useEffect } from 'react'
import { Box, Modal, Typography } from '@mui/material'
import { ChromePicker } from 'react-color'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../MUIButton'
import MUIStack from '../MUIStack'
import './ColorPicker.style.css'

interface Props {
  open: boolean
  color: string
  onChange: (color: string) => void
  onClose: () => void
  onSave: (color: string) => void
  title?: string
}

const ColorPickerModal: React.FC<Props> = ({ open, color, onChange, onClose, onSave, title }) => {
  const [currentColor, setCurrentColor] = useState(color)

  useEffect(() => {
    if (color.startsWith('rgba')) {
      const rgbaMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-1]?\.?\d*))?\)/)
      if (rgbaMatch) {
        const r = parseInt(rgbaMatch[1])
        const g = parseInt(rgbaMatch[2])
        const b = parseInt(rgbaMatch[3])
        const a = rgbaMatch[4] ? parseFloat(rgbaMatch[4]).toFixed(2) : '1.00'
        setCurrentColor(`rgba(${r}, ${g}, ${b}, ${a})`)
      } else {
        setCurrentColor(color)
      }
    } else {
      setCurrentColor(color)
    }
  }, [color])

  const handleColorChange = (newColor: any) => {
    const { r, g, b, a } = newColor.rgb
    const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a.toFixed(2)})`
    setCurrentColor(rgbaColor)
    onChange(rgbaColor)
  }

  const handleColorChangeComplete = (newColor: any) => {
    const { r, g, b, a } = newColor.rgb
    const rgbaColor = `rgba(${r}, ${g}, ${b}, ${a.toFixed(2)})`
    setCurrentColor(rgbaColor)
    onChange(rgbaColor)
  }

  return (
    <Modal
      open={open}
      onClose={onClose}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}
    >
      <MUIStack
        sx={{
          width: 450,
          outline: 'none',
          bgcolor: 'background.paper',
          borderRadius: 4,
          boxShadow: 24,
          m: '0px 20px',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          p: 3,
        }}
      >
        <Typography variant="h6" component="h2" gutterBottom>
          {title || 'Pick a color'}
        </Typography>

        <Box width={'100%'}>
          <ChromePicker
            color={currentColor}
            onChange={handleColorChange}
            onChangeComplete={handleColorChangeComplete}
            disableAlpha={false}
          />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'end', gap: 2, mt: 2 }}>
          <MUIButton variant="outlined" onClick={onClose} sx={{ borderRadius: 2, minWidth: 140 }}>
            Cancel
          </MUIButton>
          <MUIButton variant="contained" onClick={() => onSave(currentColor)} sx={{ borderRadius: 2, minWidth: 140 }}>
            Save
          </MUIButton>
        </Box>
      </MUIStack>
    </Modal>
  )
}

export default ColorPickerModal
