import React, { ReactNode } from 'react'
import { Button, ButtonProps } from '@mui/material'
import { MUIStyled } from './MUIStyled'

interface StyledButtonProps extends ButtonProps {
  rounded?: boolean
  color?: any
}

const StyledButton = MUIStyled(Button, {
  shouldForwardProp: (prop) => prop !== 'rounded',
})<StyledButtonProps>(({ theme, rounded }) => ({
  borderRadius: rounded ? 10 : 30,
  '&.MuiButton-outlinedSecondary': {
    color: theme.palette.gray?.light,
  },
  '&.MuiButton-outlinedGray': {
    color: theme.palette.gray?.light,
  },
}))
interface MUIButtonProps extends StyledButtonProps {
  children: ReactNode
}

const MUIButton: React.FC<MUIButtonProps> = ({ rounded, children, ...props }) => {
  return (
    <StyledButton disableElevation rounded={rounded} {...props}>
      {children}
    </StyledButton>
  )
}

export default MUIButton
