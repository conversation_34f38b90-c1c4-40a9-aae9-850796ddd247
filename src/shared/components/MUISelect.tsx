import React from 'react'
import { Box, FormControl, Select } from '@mui/material'
import <PERSON><PERSON>Form<PERSON>abel from './MUIFormLabel'
import MUIMenuItem from './MUIMenuItem'
import { MUIStyled } from './MUIStyled'

interface Option {
  value: string | number
  label: string
}

interface MUISelectProps {
  label?: string
  value?: string | number
  onChange?: (e: any) => void
  options: Option[]
  placeholder?: string
  displayEmpty?: boolean
  MenuProps?: object // Add MenuProps to allow customization of the dropdown menu
  // You can add other props from the Select component here
  // ...props for Select component, which are part of SelectProps
  [key: string]: any
  startIcon?: React.ReactNode
}

export const StyleSelect = MUIStyled(FormControl)(({ theme }) => ({
  // marginBottom: 16,
  '& .MuiInputBase-root': {
    borderRadius: 12,
    backgroundColor: theme.palette.white?.main,
    borderColor: theme.palette.gray?.main,
    boxShadow: `0px 1px 2px 0px ${theme.palette.gray?.light}0D`,
    '&.MuiInputBase-multiline': {
      padding: 0,
    },
    '&:after, &:before': {
      content: 'normal',
    },
    '&.MuiFilledInput-root': {
      backgroundColor: theme.palette.background.default,
    },
    '&.MuiOutlinedInput-root': {
      '& .MuiInputBase-input': {
        fontSize: 14,
        padding: '8px 35px 8px 15px',
        color: theme.palette.gray?.light,
        '&::placeholder': {
          textTransform: 'capitalize',
        },
      },
      '& .MuiInputAdornment-root': {
        marginRight: 0,
        '& i': {
          color: theme.palette.gray?.light,
        },
        '& .MuiTypography-root': {
          fontSize: 14,
          color: theme.palette.gray?.light,
          borderRight: `1px solid ${theme.palette.gray?.main}`,
          paddingRight: 10,
          lineHeight: 3,
        },
      },
    },
    '& i': {
      fontSize: 20,
      padding: 8,
      pointerEvents: 'none',
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      display: 'flex',
      alignItems: 'center',
      color: `${theme.palette.darkGray?.light}99`,
    },
    '&.Mui-focused': {
      boxShadow: `0px 0px 2px 0 ${theme.palette.primary.light100}80`,
      '& .MuiOutlinedInput-notchedOutline': {
        borderWidth: 1,
        borderColor: theme.palette.primary.light200,
      },
    },
    '&:hover': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.light200,
      },
    },
  },
  '& .MuiFormHelperText-root': {
    margin: 0,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '& svg': {
      width: 15,
      height: 15,
      verticalAlign: 'middle',
    },
  },
}))

const MUISelect: React.FC<MUISelectProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder,
  displayEmpty = false,
  MenuProps,
  startIcon,
  ...props
}) => {
  const defaultMenuProps = {
    PaperProps: {
      style: {
        maxHeight: 300,
        overflow: 'auto',
      },
    },
  }

  const mergedMenuProps = MenuProps ? { ...defaultMenuProps, ...MenuProps } : defaultMenuProps

  return (
    <StyleSelect fullWidth>
      {label && <MUIFormLabel>{label}</MUIFormLabel>}
      {startIcon && (
        <Box
          sx={{
            position: 'absolute',
            left: 10,
            top: '50%',
            zIndex: 1,
            pointerEvents: 'none',
            color: 'text.secondary',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {startIcon}
        </Box>
      )}
      <Select
        value={value}
        onChange={onChange}
        displayEmpty={placeholder ? true : false}
        IconComponent={(props) => <i className="ri-arrow-down-s-line"></i>}
        MenuProps={mergedMenuProps}
        {...props}
        sx={{
          pl: startIcon ? 2 : 0,
          ...props.sx,
        }}
      >
        {placeholder && (
          <MUIMenuItem disabled value="">
            {placeholder}
          </MUIMenuItem>
        )}
        {options?.map((option) => (
          <MUIMenuItem key={option.value} value={option.value}>
            {option.label}
          </MUIMenuItem>
        ))}
      </Select>
    </StyleSelect>
  )
}

export default MUISelect
