import { IAutoComplateOption } from '../autocomplate/interface'

export type IAutoCompleteSearch = {
  labelTitle?: string
  label?: string
  options: IAutoComplateOption[]
  onChange: (value: IAutoComplateOption | null) => void
  loading?: boolean
  groupByKey?: string
  filterOptions?: (options: IAutoComplateOption[], state: { inputValue: string }) => IAutoComplateOption[]
  renderOption?: (props: any, option: IAutoComplateOption) => React.ReactNode
  onFocus?: () => void
  value?: IAutoComplateOption | null
}
