import React from 'react'
import { IAutoCompleteSearch } from './interface'
import AutoComplete from '@/shared/components/autocomplate'
import MUIBox from '@/shared/components/MUIBox'
import MUITypography from '@/shared/components/MUITypography'

const AutoCompleteSearch: React.FC<IAutoCompleteSearch> = ({
  labelTitle,
  label,
  options,
  onChange,
  groupByKey,
  filterOptions,
  renderOption,
  onFocus,
  value,
}) => {
  return (
    <MUIBox>
      {labelTitle && (
        <MUITypography fontWeight="bold" mb={1}>
          {labelTitle}
        </MUITypography>
      )}
      <AutoComplete
        options={options}
        label={label || ''}
        onChange={(value) => onChange(value)}
        loading={false}
        minSearchLength={3}
        groupByKey={groupByKey}
        startAdornment={<i className="ri-search-line"></i>}
        filterOptions={filterOptions}
        renderOption={renderOption}
        onFocus={onFocus}
        value={value}
      />
    </MUIBox>
  )
}

export default AutoCompleteSearch
