import React, { useState } from 'react'
import { Autocomplete, AutocompleteRenderGroupParams, InputAdornment } from '@mui/material'
import MUITextField from '../MUITextField'
import styles from './Autocomplate.scss'
import { GroupHeader, GroupItems } from './AutoComplete.style'
import { useFilteredOptions } from './hooks/useFilteredOptions'
import { IAutoComplateOption, IAutoCompleteProps } from './interface'
import { useDebounce } from '@/shared/hooks/useDebounce'

const AutoComplete: React.FC<IAutoCompleteProps> = ({
  options,
  onChange,
  label,
  loading = false,
  groupByKey,
  filterOptions,
  renderOption,
  startAdornment,
  endAdornment,
  minSearchLength = 0, // Prop to set the minimum input length
  debounceTime = 300, // Prop for debounce duration
  onFocus,
  value,
}) => {
  const [inputValue, setInputValue] = useState('')
  const debouncedValue = useDebounce(inputValue, debounceTime)
  const filteredOptions = useFilteredOptions(debouncedValue, options, filterOptions)

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const searchValue = event.target.value
    setInputValue(searchValue)
  }

  const renderCustomOption =
    renderOption ||
    ((props, option) => (
      <li
        {...props}
        key={option.value}
        className={styles}
        style={{
          padding: '5px 10px',
          cursor: 'pointer',
        }}
      >
        {option.label}
      </li>
    ))

  const renderCustomGroup = (params: AutocompleteRenderGroupParams) => (
    <li key={params.key}>
      <GroupHeader>{params.group}</GroupHeader>
      <GroupItems>{params.children}</GroupItems>
    </li>
  )

  const groupBy = groupByKey
    ? (option: IAutoComplateOption) => option[groupByKey as keyof IAutoComplateOption] as string
    : undefined

  return (
    <Autocomplete
      options={debouncedValue.length >= minSearchLength ? filteredOptions : []}
      groupBy={groupBy}
      getOptionLabel={(option) => option.label}
      loading={loading && debouncedValue.length >= minSearchLength}
      onInputChange={(event, value) => setInputValue(value)}
      onChange={(_, value) => onChange(value)}
      filterOptions={filterOptions}
      renderInput={(params) => (
        <MUITextField
          {...params}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={onFocus}
          placeholder={`Search by ${label}`}
          fullWidth
          variant="outlined"
          size="small"
          sx={{
            '& .MuiOutlinedInput-root': {
              padding: '2px 7px !important',
            },
          }}
          slotProps={{
            input: {
              ...params.InputProps,
              startAdornment: startAdornment ? (
                <InputAdornment position="start">{startAdornment}</InputAdornment>
              ) : null,
              endAdornment: endAdornment ? <InputAdornment position="end">{endAdornment}</InputAdornment> : null,
            },
          }}
        />
      )}
      renderOption={renderCustomOption}
      renderGroup={renderCustomGroup}
      noOptionsText={
        debouncedValue.length < minSearchLength ? `Type at least ${minSearchLength} characters` : 'No results found'
      }
      value={value}
    />
  )
}

export default AutoComplete
