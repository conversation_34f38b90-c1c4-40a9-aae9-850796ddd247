import { useEffect, useState } from 'react'
import { IAutoComplateOption } from '../interface'

export const useFilteredOptions = (
  inputValue: string,
  options: IAutoComplateOption[],
  filterOptions?: (options: IAutoComplateOption[], state: { inputValue: string }) => IAutoComplateOption[],
) => {
  const [filteredOptions, setFilteredOptions] = useState(options)

  useEffect(() => {
    if (filterOptions) {
      setFilteredOptions(filterOptions(options, { inputValue }))
    } else {
      setFilteredOptions(options.filter((item) => item.label.toLowerCase().includes(inputValue.toLowerCase())))
    }
  }, [inputValue, options, filterOptions])

  return filteredOptions
}
