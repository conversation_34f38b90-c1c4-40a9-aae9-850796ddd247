export interface IAutoComplateOption {
  label: string
  value: string
  [key: string]: string
}

export interface IAutoCompleteProps {
  options: IAutoComplateOption[]
  label: string
  onChange: (value: IAutoComplateOption | null) => void
  loading?: boolean
  groupByKey?: string
  filterOptions?: (options: IAutoComplateOption[], state: { inputValue: string }) => IAutoComplateOption[]
  renderOption?: (props: any, option: IAutoComplateOption) => React.ReactNode
  startAdornment?: React.ReactNode
  endAdornment?: React.ReactNode
  minSearchLength?: number
  debounceTime?: number
  onFocus?: () => void
  value?: IAutoComplateOption | null
}
