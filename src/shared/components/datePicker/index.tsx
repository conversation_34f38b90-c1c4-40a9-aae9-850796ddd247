import { LocalizationProvider, DatePicker as MuiDatePicker } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'

interface MuiDatePickerProps {
  value: any
  onChange?: (date: any, keyboardInputValue?: string) => void
}

const DatePicker: React.FC<MuiDatePickerProps> = ({ value, onChange = () => null, ...props }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MuiDatePicker {...props} format="dd/MM/yyyy" />
    </LocalizationProvider>
  )
}

export default DatePicker
