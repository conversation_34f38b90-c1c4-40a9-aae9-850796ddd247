import { MobileDatePicker } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'

interface IMuiDatePicker {
  value: Date
  onChange: (args: Date) => void
  onAccept?: (args: Date) => void
  format?: string
}

const MUIDatePicker: React.FC<IMuiDatePicker> = ({ value, onChange, format, onAccept, ...props }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MobileDatePicker
        {...props}
        value={value}
        onChange={(e) => onChange(e as Date)}
        format={format}
        defaultValue={new Date()}
        onAccept={(e) => onAccept?.(e as Date)}
      />
    </LocalizationProvider>
  )
}

export default MUIDatePicker
