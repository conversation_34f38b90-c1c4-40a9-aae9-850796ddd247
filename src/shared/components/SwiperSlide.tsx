import React, { ReactNode } from 'react'
import { Pagination } from 'swiper/modules'

import 'swiper/css'
import 'swiper/css/pagination'
import { Swiper, SwiperProps } from 'swiper/react'
import M<PERSON>Stack from './MUIStack'

// Define props for the component
interface SwiperSliderProps extends SwiperProps {
  children: ReactNode
}

const SwiperSlider: React.FC<SwiperSliderProps> = ({ children, ...props }) => {
  return (
    <MUIStack sx={{ '& .swiper ': { width: '100%' } }}>
      <Swiper modules={[Pagination]} {...props}>
        {children}
      </Swiper>
    </MUIStack>
  )
}

export default SwiperSlider
