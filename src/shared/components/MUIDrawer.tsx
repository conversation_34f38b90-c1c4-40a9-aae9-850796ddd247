import { Drawer, <PERSON><PERSON><PERSON>utt<PERSON>, DrawerP<PERSON> } from '@mui/material'
import <PERSON><PERSON><PERSON>ox from './MUIBox'

// Typing for the props
interface MUIDrawerProps extends DrawerProps {
  handleClose: () => void // Function to handle close
  children: React.ReactNode // To support children elements
}

const MUIDrawer: React.FC<MUIDrawerProps> = ({ children, handleClose, ...props }) => {
  return (
    <Drawer {...props}>
      <MUIBox textAlign="left">
        <IconButton onClick={handleClose}>
          <i className="ri-arrow-left-line"></i>
        </IconButton>
      </MUIBox>
      {children}
    </Drawer>
  )
}

export default MUIDrawer
