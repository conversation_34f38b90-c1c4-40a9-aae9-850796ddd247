import React, { useEffect, useRef } from 'react'
import { toast } from 'sonner'

const getUserPermissions = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  return user?.permissions || [] // Return an array of permission ids
}

const hasPermission = (requiredPermission: string) => {
  const userPermissions = getUserPermissions()
  return userPermissions.includes(requiredPermission)
}

interface AuthGuardProps {
  requiredPermission: string
  element: React.ReactNode
}

const AuthGuard: React.FC<AuthGuardProps> = ({ requiredPermission, element }) => {
  const toastShown = useRef(false)

  useEffect(() => {
    if (!hasPermission(requiredPermission) && !toastShown.current) {
      toast.error('You do not have permission to access this page.')
      toastShown.current = true
    }
  }, [requiredPermission]) // Trigger effect only when requiredPermission changes

  if (hasPermission(requiredPermission)) {
    return <>{element}</> // Render the element if permission is granted
  }

  return null // Return nothing if permission is not granted
}

export default AuthGuard
