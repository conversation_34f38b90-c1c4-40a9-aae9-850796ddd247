import React from 'react'
import { Checkbox, FormControlLabel, FormGroup, CheckboxProps } from '@mui/material'

interface MUICheckboxProps extends CheckboxProps {
  label?: string
  color?: any
}

const MUICheckbox: React.FC<MUICheckboxProps> = ({ label, color = 'primary', ...props }) => {
  return (
    <FormGroup>
      <FormControlLabel
        control={<Checkbox color={color} {...props} />}
        label={label}
        sx={{ '& .MuiTypography-root': { fontWeight: 600 } }}
      />
    </FormGroup>
  )
}

export default MUICheckbox
