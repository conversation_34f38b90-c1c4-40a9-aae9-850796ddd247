import React, { useState, useEffect } from 'react'
import { InputAdornment } from '@mui/material'
import MUITypography from './MUITypography'
import {
  CashInput,
  DialNumber,
  FixPrice,
} from '@/features/appointments/components/addClientDetail/AddClientDetail.style'
import M<PERSON><PERSON>ox from '@/shared/components/MUIBox'
import M<PERSON><PERSON>utton from '@/shared/components/MUIButton'
import MUIDialog from '@/shared/components/MUIDialog'
import MUIStack from '@/shared/components/MUIStack'
import MUITextField from '@/shared/components/MUITextField'

interface CashAmountProps {
  open: boolean
  onClose: () => void
  amount: string
  onPay?: (value: string) => void
  onChange?: (value: string) => void
  isLoading?: boolean
  isVoucherPurchase?: number | boolean
  voucherPurchaseWarning?: string
  multicheckoutWarning?: string
  title?: string
  maxAmount?: string
}

const dialNumber = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '0', <i className="ri-delete-back-2-line" />]

const AmountModal: React.FC<CashAmountProps> = ({
  open,
  onClose,
  amount,
  onChange,
  onPay,
  isLoading,
  isVoucherPurchase = false,
  title,
  voucherPurchaseWarning,
  maxAmount,
  multicheckoutWarning,
}) => {
  const [value, setValue] = useState(() => {
    if (maxAmount) {
      return parseFloat(amount) < parseFloat(maxAmount) ? amount : maxAmount
    }
    return amount
  })
  const numericMaxAmount = maxAmount ? parseFloat(maxAmount) : 0

  // Update internal value when amount prop changes
  useEffect(() => {
    if (maxAmount) {
      setValue(parseFloat(amount) < parseFloat(maxAmount) ? amount : maxAmount)
    } else {
      setValue(amount)
    }
  }, [amount, maxAmount])

  const fixPriceHandler = (price: string) => {
    if (isVoucherPurchase) return
    if (multicheckoutWarning) return
    const numericPrice = parseFloat(price)

    const finalValue = numericPrice <= numericMaxAmount ? price : maxAmount || price
    onChange?.(finalValue)
    setValue(finalValue)
  }

  const handleDialNumberClick = (item: any) => {
    if (isVoucherPurchase) return
    if (multicheckoutWarning) return
    let newValue = value

    if (typeof item === 'string') {
      if (item === '.') {
        if (!newValue.includes('.')) {
          newValue = (newValue || '0') + item
        }
      } else {
        newValue = newValue === '0' ? item : newValue + item
      }
    } else {
      // When backspacing, ensure we never end up with an empty value
      newValue = newValue.length > 1 ? newValue.slice(0, -1) : '0'
    }
    const numericValue = parseFloat(newValue)

    if (numericValue <= numericMaxAmount) {
      onChange?.(newValue)
      setValue(newValue)
    } else {
      // If the new value exceeds maxAmount, set to maxAmount
      onChange?.(maxAmount || newValue)
      setValue(maxAmount || newValue)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (isVoucherPurchase) return
    if (multicheckoutWarning) return

    const inputValue = e.target.value

    if (/^\d*\.?\d*$/.test(inputValue)) {
      let newValue = inputValue === '' ? '0' : inputValue

      const numericValue = parseFloat(newValue)
      if (numericValue <= numericMaxAmount) {
        onChange?.(newValue)
        setValue(newValue)
      } else {
        // If the new value exceeds maxAmount, set to maxAmount
        onChange?.(maxAmount || newValue)
        setValue(maxAmount || newValue)
      }
    }
  }

  return (
    <MUIDialog
      open={open}
      handleClose={onClose}
      maxWidth="xs"
      dialogContentSX={{ pt: 0 }}
      title={title ? title : 'Add cash amount'}
    >
      {isVoucherPurchase && multicheckoutWarning ? (
        <MUITypography variant="body2" color="error">
          You can not split multi-checkout or Voucher purchase
        </MUITypography>
      ) : isVoucherPurchase ? (
        <MUITypography variant="body2" color="error">
          {voucherPurchaseWarning}
        </MUITypography>
      ) : multicheckoutWarning ? (
        <MUITypography variant="body2" color="error">
          {multicheckoutWarning}
        </MUITypography>
      ) : null}

      {!(isVoucherPurchase || multicheckoutWarning) && maxAmount && (
        <MUITypography variant="body2" color="error">
          Cannot add more than the maximum amount.
        </MUITypography>
      )}

      <CashInput>
        <MUITextField
          variant="standard"
          value={value}
          onChange={handleInputChange}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment
                  position="start"
                  sx={{
                    '.MuiTypography-root': {
                      fontWeight: 'bold',
                      fontSize: '32px',
                      color: '#000000de',
                    },
                  }}
                >
                  EUR
                </InputAdornment>
              ),
            },
          }}
        />
      </CashInput>

      <FixPrice direction="row" spacing={1}>
        {[45, 50, 55, 60].map((item) => (
          <MUIButton variant="outlined" key={item} onClick={() => fixPriceHandler(item.toString())}>
            €{item}
          </MUIButton>
        ))}
      </FixPrice>

      <DialNumber direction="row">
        {dialNumber.map((item, i) => (
          <MUIBox key={i}>
            <MUIButton variant="outlined" onClick={() => handleDialNumberClick(item)}>
              {item}
            </MUIButton>
          </MUIBox>
        ))}
      </DialNumber>

      <MUIStack mt={3} direction="row" justifyContent="space-between" alignItems="center">
        <div />
        <MUIButton
          rounded
          sx={{ minWidth: 120 }}
          variant="contained"
          color="primary"
          onClick={() => onPay?.(value)}
          disabled={isLoading}
        >
          {isLoading ? 'Processing...' : 'Pay now'}
        </MUIButton>
      </MUIStack>
    </MUIDialog>
  )
}

export default AmountModal
