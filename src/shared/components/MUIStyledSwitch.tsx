import React from 'react'
import { styled, keyframes } from '@mui/material/styles'
import Switch, { SwitchProps } from '@mui/material/Switch'

interface OnlineOfflineSwitchProps {
  checked: boolean
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  disabled?: boolean
  loading?: boolean
}

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`

const StyledSwitch = styled((props: SwitchProps & { loading?: boolean }) => {
  const { loading, ...switchProps } = props
  return <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...switchProps} />
})<{ loading?: boolean }>(({ theme, loading }) => ({
  width: 56,
  height: 24,
  padding: 0,
  display: 'flex',
  '& .MuiSwitch-switchBase': {
    padding: 2,
    margin: 2,
    transitionDuration: loading ? '0ms' : '300ms',
    '&.Mui-checked': {
      transform: 'translateX(34px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: '#43a047', // ✅ now green means Online (reversed)
        opacity: 1,
        '&::before': {
          content: '"Online"',
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-48%)',
          left: 1,
          backgroundColor: '#43a047',
          color: '#fff',
          fontSize: 9,
          fontWeight: 550,
          fontFamily: 'Arial, sans-serif',
          borderRadius: '50%',
          padding: '2px 4px',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
        },
      },
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 15,
    height: 15,
    backgroundColor: loading ? 'transparent' : '#fff',
    boxShadow: loading ? 'none' : '0 1px 3px rgba(0,0,0,0.3)',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&::after': loading
      ? {
          content: '""',
          position: 'absolute',
          width: 10,
          height: 10,
          border: '2px solid #ccc',
          borderTop: '2px solid #666',
          borderRadius: '50%',
          animation: `${spin} 1s linear infinite`,
        }
      : {},
  },
  '& .MuiSwitch-track': {
    borderRadius: 11,
    backgroundColor: '#c62828', // ✅ red now means Offline (reversed)
    opacity: 1,
    position: 'relative',
    transition: theme.transitions.create(['background-color'], { duration: 300 }),
    '&::before': {
      content: '"Offline"',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-48%)',
      right: 2,
      backgroundColor: '#c62828',
      color: '#fff',
      fontSize: 9,
      fontWeight: 550,
      fontFamily: 'Arial, sans-serif',
      borderRadius: '50%',
      padding: '2px 6px',
      whiteSpace: 'nowrap',
      pointerEvents: 'none',
    },
  },
}))

const MUISwitchToggle: React.FC<OnlineOfflineSwitchProps> = ({
  checked,
  onChange,
  disabled = false,
  loading = false,
}) => {
  return <StyledSwitch checked={checked} onChange={onChange} disabled={disabled || loading} loading={loading} />
}

export default MUISwitchToggle
