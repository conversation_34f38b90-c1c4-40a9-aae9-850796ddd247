import React from 'react'
import { FormControlLabel, Switch, SwitchProps } from '@mui/material'
import { MUIStyled } from './MUIStyled'

const IOSSwitch = MUIStyled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 34,
  height: 20,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(14px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: theme.palette.success.main,
        opacity: 1,
        border: 0,
        ...theme.applyStyles('dark', {
          backgroundColor: theme.palette.success.main,
        }),
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      color: '#33cf4d',
      border: '6px solid #fff',
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color: theme.palette.grey[100],
      ...theme.applyStyles('dark', {
        color: theme.palette.grey[600],
      }),
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: 0.7,
      ...theme.applyStyles('dark', {
        opacity: 0.3,
      }),
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 16,
    height: 16,
  },
  '& .MuiSwitch-track': {
    borderRadius: 20 / 2,
    backgroundColor: '#E9E9EA',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
    ...theme.applyStyles('dark', {
      backgroundColor: '#39393D',
    }),
  },
}))

interface MUISwitchProps {
  label?: string
  checked?: boolean
  onChange?: SwitchProps['onChange']
  sx?: object
  onClick?: (props: SwitchProps) => void
  disabled?: boolean
}
const MUISwitch: React.FC<MUISwitchProps> = ({ label = '', checked, onChange, sx, onClick, disabled }) => {
  return (
    <FormControlLabel
      sx={{ ml: 0, ...sx }}
      control={<IOSSwitch checked={checked} onChange={onChange} onClick={onClick} disabled={disabled} />}
      label={label}
    />
  )
}

export default MUISwitch
