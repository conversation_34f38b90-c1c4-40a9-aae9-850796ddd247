import React, { useRef } from 'react'
import { Box, Button, ButtonProps, IconButton } from '@mui/material'
import MUITypography from './MUITypography'
import theme from '@/styles/theme'

interface MUIImageUploadProps {
  onChange: (file: File | null) => void
  helperText?: string
  error: string
  buttonProps?: ButtonProps
  minSizeKB?: number
  maxSizeMB?: number
  previewUrl?: string
}

const MUIImageUpload: React.FC<MUIImageUploadProps> = ({
  onChange,
  helperText = 'Min size 24Kb, max size 15Mb',
  error,
  buttonProps,
  previewUrl,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    onChange(file)
  }

  const handleRemoveImage = () => {
    onChange(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  return (
    <Box display="flex" alignItems="start" flexDirection="column" mb={1}>
      <input ref={inputRef} type="file" accept="image/*" style={{ display: 'none' }} onChange={handleFileChange} />

      <Box position="relative">
        {previewUrl ? (
          <>
            <img
              src={previewUrl}
              alt="upload"
              width={120}
              height={120}
              style={{ borderRadius: '50%', objectFit: 'contain', border: '1px solid rgba(0,0,0,0.1)' }}
            />
          </>
        ) : (
          <Button
            variant="outlined"
            onClick={() => inputRef.current?.click()}
            sx={{
              width: 120,
              height: 120,
              borderRadius: '50%',
              textTransform: 'none',
              background: `${theme.palette.lightGray?.light100} !important`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              padding: 0,
              position: 'relative',
              border: '1px solid transparent',
            }}
            {...buttonProps}
          >
            <i
              className="ri-upload-2-line"
              style={{
                fontSize: 20,
                color: theme.palette.gray?.light,
              }}
            />
            <MUITypography variant="body2" fontWeight={600} color="gray.light">
              Upload
            </MUITypography>
          </Button>
        )}

        {previewUrl && (
          <IconButton
            onClick={handleRemoveImage}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              width: 20,
              height: 20,
              backgroundColor: 'error.light',
              color: 'white !important',
              '&:hover': {
                backgroundColor: 'error.main',
              },
              boxShadow: 2,
            }}
            size="small"
          >
            <i className="ri-close-line"></i>
          </IconButton>
        )}
      </Box>

      <MUITypography variant="caption" color="error" mt={1}>
        {error}
      </MUITypography>
      <MUITypography variant="caption" color="text.secondary">
        {helperText}
      </MUITypography>
    </Box>
  )
}

export default MUIImageUpload
