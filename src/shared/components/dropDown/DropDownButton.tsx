import React from 'react'
import { Avatar } from '@mui/material'
import <PERSON><PERSON><PERSON>utt<PERSON> from '../MUIButton'
import DropDownTextField from './DropDownTextField'
import { IDropdownButtonProps } from './interface'
import M<PERSON>Box from '@/shared/components/MUIBox'

const DropdownButton: React.FC<IDropdownButtonProps> = ({
  showSearchField,
  searchText,
  onSearchChange,
  onClearSearch,
  value,
  options,
  onOpen,
  startAdornment,
  endAdornment,
}) => {
  return showSearchField ? (
    <MUIBox sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
      <DropDownTextField
        searchText={searchText}
        onSearchChange={onSearchChange}
        onClick={(event: React.MouseEvent<HTMLInputElement>) => {
          event.stopPropagation() // Prevent menu closure
          onOpen(event)
          onClearSearch()
        }}
        startAdornment={startAdornment}
        endAdornment={endAdornment}
      />
    </MUIBox>
  ) : (
    <MUIButton onClick={onOpen} aria-haspopup="true" aria-expanded={showSearchField}>
      {options.length > 0 && options[0]?.avatarSrc && (
        <Avatar src={options[0].avatarSrc} sx={{ width: 24, height: 24, mr: 1 }} />
      )}
      {value || 'Select an option'}
      <i className="ri-arrow-down-s-line" />
    </MUIButton>
  )
}

export default DropdownButton
