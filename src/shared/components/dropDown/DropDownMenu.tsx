import React from 'react'
import { Avatar } from '@mui/material'
import MUIMenu from '../MUIMenu'
import MUIMenuItem from '../MUIMenuItem'
import { IDropdownMenuProps } from './interface'

const DropdownMenu: React.FC<IDropdownMenuProps> = ({
  anchorEl,
  options,
  searchText,
  onSelect,
  onClose,
  renderOption,
}) => {
  const isOpen = Boolean(anchorEl)

  return (
    <MUIMenu
      anchorEl={anchorEl}
      open={isOpen}
      onClose={onClose}
      disableAutoFocusItem
      isMouseDown={true}
      paper={{
        sx: {
          maxWidth: 360,
          mt: 1,
          width: '100%',
          '& .MuiMenuItem-root': {
            fontSize: 14,
            fontWeight: 500,
            padding: '10px 8px',
            borderRadius: 0,
            '&:not(:last-child)': {
              borderBottom: '1px solid #E0E0E0',
            },
          },
        },
        // onMouseDown: (event: React.MouseEvent) => event.stopPropagation(), // Prevent menu closure when interacting
      }}
    >
      {options
        .filter((option) => option.label.toLowerCase().includes(searchText.toLowerCase()))
        .map((option, index) => (
          <MUIMenuItem
            key={index}
            onClick={() => {
              onSelect(option)
              onClose()
            }}
          >
            {renderOption ? (
              renderOption(option)
            ) : (
              <>
                {option.avatarSrc && <Avatar sx={{ width: 20, height: 20, mr: 1 }} src={option.avatarSrc} />}
                {option.label}
              </>
            )}
          </MUIMenuItem>
        ))}
    </MUIMenu>
  )
}

export default DropdownMenu
