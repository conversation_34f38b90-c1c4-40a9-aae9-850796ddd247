export type IDropDownProps = {
  label: string
  options: IDropdownOption[]
  value: string
  onOptionSelect: (option: IDropdownOption) => void
  onSearchChange?: (searchText: string) => void
  renderOption?: (option: IDropdownOption) => React.ReactNode
  showSearchField?: boolean
  startAdornment?: React.ReactNode
  endAdornment?: React.ReactNode
}

export type IDropdownButtonProps = {
  showSearchField: boolean
  searchText: string
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClearSearch: () => void
  value: string
  options: IDropdownOption[]
  onOpen: (event: React.MouseEvent<HTMLButtonElement | HTMLInputElement>) => void
  startAdornment?: React.ReactNode
  endAdornment?: React.ReactNode
}

export type IDropdownMenuProps = {
  anchorEl: HTMLElement | null
  options: IDropdownOption[]
  searchText: string
  onSelect: (option: IDropdownOption) => void
  onClose: () => void
  renderOption?: (option: IDropdownOption) => React.ReactNode
}

export type IDropdownOption = {
  label: string
  avatarSrc?: string
}

export type IDropDownTextFieldProps = {
  searchText: string
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClick: (event: React.MouseEvent<HTMLInputElement>) => void // Correct type
  startAdornment?: React.ReactNode // Custom startAdornment
  endAdornment?: React.ReactNode // Custom endAdornment
}
