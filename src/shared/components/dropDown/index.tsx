import React, { useState } from 'react'
import DropdownButton from './DropDownButton'
import DropdownMenu from './DropDownMenu'
import MUI<PERSON><PERSON><PERSON>abel from '../MUIFormLabel'
import MUIGrid from '../MUIGrid'
import { IDropdownOption, IDropDownProps } from './interface'

const DropDown: React.FC<IDropDownProps> = ({
  label,
  options,
  value,
  onOptionSelect,
  onSearchChange,
  renderOption,
  showSearchField = false,
  startAdornment,
  endAdornment,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [searchText, setSearchText] = useState('')

  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const updatedSearchText = event.target.value
    setSearchText(updatedSearchText)
    onSearchChange && onSearchChange(updatedSearchText)
  }

  const handleClearSearch = () => {
    setSearchText('')
    onSearchChange && onSearchChange('')
  }

  const handleSelect = (option: IDropdownOption) => {
    onOptionSelect(option)
    handleClose()
  }

  return (
    <MUIGrid size={{ xs: 12, md: 12 }}>
      <MUIFormLabel>{label}</MUIFormLabel>
      <DropdownButton
        showSearchField={showSearchField}
        searchText={searchText}
        onSearchChange={handleSearchChange}
        onClearSearch={handleClearSearch}
        value={value}
        options={options}
        onOpen={handleOpen}
        startAdornment={startAdornment}
        endAdornment={endAdornment}
      />
      <DropdownMenu
        anchorEl={anchorEl}
        options={options}
        searchText={searchText}
        onSelect={handleSelect}
        onClose={handleClose}
        renderOption={renderOption}
      />
    </MUIGrid>
  )
}

export default DropDown
