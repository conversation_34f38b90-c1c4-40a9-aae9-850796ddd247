import React from 'react'
import { InputAdornment } from '@mui/material'
import MUITextField from '../MUITextField'
import { IDropDownTextFieldProps } from './interface'

const DropDownTextField: React.FC<IDropDownTextFieldProps> = ({
  searchText,
  onSearchChange,
  onClick,
  startAdornment, // Receive startAdornment
  endAdornment, // Receive endAdornment
}) => {
  return (
    <MUITextField
      value={searchText}
      onChange={onSearchChange}
      onClick={(e) => onClick(e as React.MouseEvent<HTMLInputElement>)}
      placeholder="Search by service name"
      fullWidth
      variant="outlined"
      size="small"
      slotProps={{
        input: {
          startAdornment: startAdornment ? <InputAdornment position="start">{startAdornment}</InputAdornment> : null,
          endAdornment: endAdornment ? <InputAdornment position="end">{endAdornment}</InputAdornment> : null,
        },
      }}
    />
  )
}

export default DropDownTextField
