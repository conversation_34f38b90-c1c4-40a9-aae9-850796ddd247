import { addDays, addMonths, format } from 'date-fns'

export const getExpiryDate = (
  frequency: number,
  fromDateStr: string,
): { expiryDate: Date; formattedExpiryDate: string } => {
  const fromDate = new Date(fromDateStr)

  let expiryDate: Date
  switch (frequency) {
    case 0: //* Once (First time booking only)
      expiryDate = addDays(fromDate, 1)
      break
    case 1: //* One Month
      expiryDate = addMonths(fromDate, 1)
      break
    case 3: //* One quarter (4 months)
      expiryDate = addMonths(fromDate, 4)
      break
    case 6: //*6 months
      expiryDate = addMonths(fromDate, 6)
      break
    case 12: //*12 months
      expiryDate = addMonths(fromDate, 12)
      break
    default:
      throw new Error('Invalid frequency value')
  }

  return {
    expiryDate,
    formattedExpiryDate: format(expiryDate, "yyyy-MM-dd'T'HH:mm:ss"),
  }
}
