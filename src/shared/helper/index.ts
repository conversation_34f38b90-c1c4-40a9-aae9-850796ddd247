import { differenceInMinutes, isBefore, parseISO } from 'date-fns'
import { IStaffDetails } from '../hooks/apis/getAllStaff'
import { IPagePermissions, useGetPermissions } from '../hooks/apis/permissions/getPermissions'
import { ISelectedServices } from '@/redux/slice/calenderSlice/interface'
import useStaffLoginDetailsSliceAction from '@/redux/slice/staffLoginSlice/useStaffLoginDetailsSliceAction'

export const getFirstLetter = (str: string) => {
  return str?.charAt(0)?.toUpperCase() // Capitalizes the first letter
}

export const getStaffById = (id: string, staffs: IStaffDetails[]) => {
  const findStaff = staffs.find((staff) => staff.employee_id.toString() === id)
  return findStaff || null
}

export const getValidStaffByService = (
  newService: ISelectedServices,
  preStaffId: string | number,
  reorderStaffListByRoleAndName: IStaffDetails[],
) => {
  //* Get Last service staff
  const prevStaff = getStaffById(preStaffId.toString(), reorderStaffListByRoleAndName)

  //*Check is staff provide new service or not
  const isPevStaffProvideService = prevStaff?.performedServices?.includes(Number(newService.id) as number)

  if (isPevStaffProvideService) {
    return prevStaff
  } else {
    const filterStaffByProvidingService = reorderStaffListByRoleAndName.filter((item) =>
      item.performedServices.includes(Number(newService.id) as number),
    )
    return filterStaffByProvidingService[0] || null
  }
}

export const reorderStaffByRoleAndName = (staff: IStaffDetails[]) => {
  return [...staff].sort((a, b) => {
    // Compare roles: higher roles come first
    if (a.role !== b.role) {
      return b.role - a.role
    }

    // If roles are the same, compare names alphabetically
    const nameA = `${a.firstName} ${a.lastName}`.trim().toLowerCase()
    const nameB = `${b.firstName} ${b.lastName}`.trim().toLowerCase()

    return nameA.localeCompare(nameB)
  })
}

export function isDateTimePassed(dateTimeString: string) {
  const givenDate = parseISO(dateTimeString)
  const currentDate = new Date()
  return isBefore(givenDate, currentDate)
}

// get time in hours range format
export const formatTimeRange = (startTime: string, endTime: string): string => {
  const start = new Date(startTime)
  const end = new Date(endTime)

  const formattedStart = start.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })
  const formattedEnd = end.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })

  return `${formattedStart} - ${formattedEnd}`
}

// date in format like "Apr 9, 2025"
export const getFormatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' })
}

// get service duration time
export const calculateServiceDuration = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0
  return differenceInMinutes(new Date(endTime), new Date(startTime))
}

export const getFormattedDate = (dateString: string | Date | undefined) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-GB', {
    weekday: 'short',
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  }).format(date)
}

export const getFormatDuration = (duration?: string): string => {
  if (!duration || !duration.includes(':')) return ''

  const [hoursStr, minutesStr] = duration.split(':')
  const hours = parseInt(hoursStr, 10)
  const minutes = parseInt(minutesStr, 10)

  const parts: string[] = []

  if (hours) parts.push(`${hours} hr`)
  if (minutes) parts.push(`${minutes} min`)

  return parts.join(' ')
}

export const getFormatTransactionDate = (isoDate?: string): string => {
  if (!isoDate) return ''

  const date = new Date(isoDate)

  const day = new Intl.DateTimeFormat('en-GB', { weekday: 'short' }).format(date) // e.g., "Sun"
  const dayOfMonth = date.getDate() // e.g., 12
  const month = new Intl.DateTimeFormat('en-GB', { month: 'short' }).format(date) // e.g., "May"
  const year = date.getFullYear() // e.g., 2025

  let hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const ampm = hours >= 12 ? 'pm' : 'am'
  hours = hours % 12 || 12 // convert to 12-hour format

  return `${day} ${dayOfMonth} ${month} ${year} at ${hours}:${minutes}${ampm}`
}

//* Function to create a reset pin url
export interface ParsedToken {
  employeeId: string
  email: string
  timestamp: number
  isExpired: boolean
}

export const generateResetUrl = (employeeId: string | number, email: string, baseUrl?: string): string => {
  try {
    // Create a unique token (in a real app, this would be more secure)
    const timestamp = Date.now().toString()
    const tokenData = `${employeeId}-${email}-${timestamp}`

    // Use encodeURIComponent for better compatibility
    const token = encodeURIComponent(tokenData)

    // Create the full URL
    const urlBase = baseUrl || window.location.origin || 'http://localhost:3000'
    const fullUrl = `${urlBase}/reset-pin?token=${token}`

    return fullUrl
  } catch (error) {
    console.error('Error generating reset URL:', error)
    // Fallback to a simple URL if there's an error
    const urlBase = baseUrl || window.location.origin || 'http://localhost:3000'
    return `${urlBase}/reset-pin?email=${encodeURIComponent(email)}`
  }
}

//* Function to create a token for reset pin
export const parseResetToken = (token: string): ParsedToken | null => {
  try {
    const decoded = decodeURIComponent(token)
    const [employeeId, email, timestamp] = decoded.split('-')

    // Validate the parsed data
    if (!employeeId || !email || !timestamp) {
      return null
    }

    return {
      employeeId,
      email,
      timestamp: Number(timestamp),
      // Add expiration check if needed
      isExpired: Date.now() - Number(timestamp) > 24 * 60 * 60 * 1000, // 24 hours
    }
  } catch (error) {
    console.error('Error parsing reset token:', error)
    return null
  }
}

//* Staff access permission
export interface RolePermissions {
  permissions: {
    pages: IPagePermissions
  }
}

export interface PermissionsResponse {
  'BUSINESS OWNER': RolePermissions
  Manager: RolePermissions
  Staff: RolePermissions
  'Senior Staff': RolePermissions
}

export type UserRole = number

// define roles
export const ROLE_MAPPING: Record<UserRole, keyof PermissionsResponse> = {
  1: 'BUSINESS OWNER',
  2: 'Manager',
  3: 'Senior Staff',
  4: 'Staff',
}

// Route to permission mapping
export const ROUTE_PERMISSION_MAPPING: Record<string, keyof IPagePermissions> = {
  '/': 'appointments',
  '/appointments': 'appointments',
  '/sales': 'make_a_sale',
  '/clients-list': 'clients',
  '/client-profile': 'clients',
  '/catalog': 'sales_manager',
  '/team-members': 'staff',
  '/rota': 'staff',
  '/staff-access': 'staff',
  '/assigned-services': 'staff_services',
  '/performance-dashboard': 'staff',
  '/report': 'reports',
}

export const hasPermission = (
  userRole: UserRole,
  permissions: PermissionsResponse | null,
  requiredPermission: keyof IPagePermissions,
): boolean => {
  const userPermissions = getUserPermissions(userRole, permissions)
  return userPermissions?.[requiredPermission] ?? false
}

export const getUserPermissions = (
  userRole: UserRole,
  permissions: PermissionsResponse | null,
): IPagePermissions | null => {
  if (!permissions) return null

  const roleName = ROLE_MAPPING[userRole]
  return permissions[roleName]?.permissions?.pages || null
}

const PERMISSION_TO_ROUTE: Record<string, string> = {
  appointments: '/',
  staff: '/team-members',
  clients: '/clients-list',
  make_a_sale: '/sales',
  products: '/products',
  staff_services: '/assigned-services',
  reports: '/report',
  promo_codes: '/promo-codes',
  sales_manager: '/catalog',
  marketing: '/marketing',
  settings: '/settings',
}

export const getFirstAccessibleRoute = (userRole: UserRole, permissions: PermissionsResponse | null): string | any => {
  if (!permissions) return '/unauthorized'

  const userPermissions: any = getUserPermissions(userRole, permissions)
  if (!userPermissions) return '/unauthorized'

  // Iterate through the permissions object keys dynamically
  for (const permission of Object.keys(userPermissions)) {
    if (userPermissions[permission] && PERMISSION_TO_ROUTE[permission]) {
      return PERMISSION_TO_ROUTE[permission]
    }
  }

  return '/unauthorized'
}
// Custom hook for checking permissions
export const usePermissions = () => {
  const { staffLoginDetails } = useStaffLoginDetailsSliceAction()
  const { data: permission } = useGetPermissions()
  const userRole = staffLoginDetails?.role

  const checkPermission = (requiredPermission: keyof IPagePermissions): boolean => {
    return hasPermission((staffLoginDetails?.role as any) || 4, permission as any, requiredPermission)
  }

  const getUserRole = (): keyof PermissionsResponse | null => {
    if (!staffLoginDetails?.role) return null
    return ROLE_MAPPING[staffLoginDetails.role as UserRole]
  }

  const getUserPermissions = () => {
    if (!permission || !userRole) return null
    const roleName = ROLE_MAPPING[userRole]
    return permission?.[roleName]?.permissions?.pages || null
  }

  const getAccessibleHomePage = (): string => {
    if (!userRole || !permission) return '/unauthorized'

    const permissions: any = permission[ROLE_MAPPING[userRole]]?.permissions?.pages
    if (!permissions) return '/unauthorized'

    // Iterate over permissions dynamically to find the first accessible route
    for (const key of Object.keys(permissions)) {
      if (permissions[key] && PERMISSION_TO_ROUTE[key]) {
        return PERMISSION_TO_ROUTE[key]
      }
    }

    return '/unauthorized'
  }

  return {
    checkPermission,
    getUserRole,
    getAccessibleHomePage,
    getUserPermissions,
    permission,
    userRole: staffLoginDetails?.role,
  }
}

// staff access tab permission
export const TAB_PERMISSION_MAPPING: Record<string, keyof IPagePermissions> = {
  'Daily summary': 'make_a_sale',
  Appointments: 'appointments',
  Products: 'products',
  'Gift cards': 'promo_codes',
  'Service menu': 'appointments',
  Product: 'products',
  'Consultation Forms': 'appointments',
}

// color convert
// convert HEX to RGBA
export const hexToRgba = (hex: string, alpha: number = 1): string => {
  let hexColor = hex.replace('#', '')
  // Handle 8-digit HEX (with alpha)
  if (hexColor.length === 8) {
    alpha = parseInt(hexColor.slice(6, 8), 16) / 255
    hexColor = hexColor.slice(0, 6)
  }
  // Handle 3-digit HEX
  if (hexColor.length === 3) {
    hexColor = hexColor
      .split('')
      .map((char) => char + char)
      .join('')
  }
  const r = parseInt(hexColor.slice(0, 2), 16)
  const g = parseInt(hexColor.slice(2, 4), 16)
  const b = parseInt(hexColor.slice(4, 6), 16)
  // Format alpha to 2 decimal places
  return `rgba(${r}, ${g}, ${b}, ${alpha.toFixed(2)})`
}

// convert RGBA to HEX
export const rgbaToHex = (rgba: string): string => {
  if (rgba.startsWith('#')) {
    return rgba
  }
  const rgbaMatch = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-1]?\.?\d*))?\)/)
  if (rgbaMatch) {
    const r = parseInt(rgbaMatch[1])
    const g = parseInt(rgbaMatch[2])
    const b = parseInt(rgbaMatch[3])
    const a = rgbaMatch[4] ? parseFloat(rgbaMatch[4]) : 1
    const hex = '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()
    if (a < 1) {
      const alphaHex = Math.round(a * 255)
        .toString(16)
        .padStart(2, '0')
        .toUpperCase()
      return hex + alphaHex
    }
    return hex
  }
  return rgba
}
