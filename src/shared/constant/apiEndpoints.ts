export const STAFF = {
  GET_ALL_STAFF: '/staff/retrieveStaff',
  GET_STAFF_AVAILIBILITY: '/staff/getStaffRota',
  GET_BUSINESS_HOURS: 'settings/getBusinessHours',
  ADD_STAFF: '/staff/newStaff',
  DELETE_STAFF: '/staff/removeStaff',
  GET_STAFF_ROTA: 'staff/getStaffRota',
  DELETE_STAFF_ROTA: 'staff/deleteStaffRota',
  ADD_STAFF_ROTA: 'staff/addStaffRota',
  UPDATE_STAFF_ROTA: 'staff/updateStaffRota',
  UPDATE_STAFF: '/staff/updateStaff',
  GET_ALL_STAFF_FOR_REPORT: '/reports/getStaffForReport',
  VERIFY_STAFF_MEMBER_PIN: '/staff/verifyStaffMemberPin',
  RESET_PIN: '/staff/reset-pin',
  REQUEST_RESET_PIN: '/staff/request-reset-pin',
}

export const BOOKINGS = {
  GET_ALL_BOOKINGS: '/bookings/retrieveBookings',
  UPDATE_BOOKING: '/bookings/updateBooking',
  DELETE_BOOKING: '/bookings/deleteBooking',
  GET_BOOKING_BY_CLIENT_ID: 'bookings/retrieveSingleUserBookings',
  SAVE_BOOKINGS: 'bookings/saveBooking',
  SAVE_BOOKING_WITH_PAYMENT: 'bookings/saveBookingWithPayment',
  GET_ALL_RETRIEVE_BOOKINGS: '/bookings/retrieveAllBookings',
  UPDATE_NO_SHOW: '/bookings/updatenoShow',
  GET_SALE_DETAILS: '/bookings/getSaleDetails',
}

export const SERVICES = {
  GET_ALL_SERVICES: '/services/retrieveServices',
  GET_SERVICE_CATEGORY: '/services/getServiceCategories',
  GET_SERVICE_CATEGORY_COLOR: '/services/getServiceCategoryColour',
  UPDATE_SERVICE_CATEGORY: '/services/updateServiceCategory',
  UPDATE_SERVICE: '/services/updateService',
  DELETE_SERVICE_CATEGORY: `/services/deleteServiceCategory`,
  DELETE_SERVICE: `/services/deleteService`,
  NEW_SERVICE_CATEGORY: '/services/newServiceCategory',
  NEW_SERVICE: '/services/newService',
  GET_SLOT_AVAILABLE_FOR_SERVICE_DURATION: '/appointment/getSlotsAvailableForEmployeeAndServiceDuration',
  UPDATE_SERVICE_ORDER: '/services/updateServiceOrder',
}

export const USER = {
  GET_USERS: '/user/retrieveAllUsersDetails',
  ADD_USER: 'user/addUser',
  UPDATE_USER_DETAIL: 'user/updateUserDetails',
  DELETE_USER: 'user/deleteUserById',
  GET_USER_DETAILS: '/user/retrieveUserDetails',
  VALIDATE_USER_EXISTS: '/user/validateUserExists',
  GET_CLIENT_NOTES: '/user/retrieveClientNotes',
  GET_TOTAL_SALES: '/user/getTotalSales',
  GET_ADD_NOTES: '/user/addNote',
}
export const HISTORY = { GET_HISTORY: 'app/getHistory' }
export const APPOINTMENT_CHECKOUT = {
  STRIPE_CREATE_CASH_CHARGE: '/stripe/create-cash-charge',
  VOUCHER_SEND_SMS_TO_CLIENT: '/voucher/sendSMSToClient',
}

export const CATALOG = {
  CREATE_SURVEY_FORM: '/forms/addForm',
  GET_ALL_SURVEY_FORM: '/forms/getAllForms',
  DELETE_SURVEY_FORM: '/forms/deleteForm',
  UPDATE_SURVEY_FORM: '/forms/updateClientForm',
  UPDATE_CATALOG_FORM: '/forms/updateForm',
  GET_ALL_CLIENT_FORM: '/forms/getAllClientForms',
  CREATE_CLIENT_FORM: '/forms/createClientForm',
  GET_ALL_CLIENT_FORM_BY_CLIENT_ID: '/forms/getClientFormsByClientId',
}

export const STRIPE = {
  GET_FORM_SERVICE: '/forms/getFormsByService',
  SEND_CUSTOME_MESSAGE: '/messaging/customSmsMessage',
  SEND_CUSTOME_EMAIL: '/send-email/send',
  GET_STRIPE_PUBLIC_KEY: '/settings/getStripePublicKey',
  CREATE_STRIPE_CHARGE: '/stripe/create-charge',
  CREATE_NOSTRIPE_CHARGE: '/stripe/create-noshow-charge',
  CREATE_CARD_TERMINAL_CHARGE_CHARGE: '/stripe/init_payments',
  CANCEL_PAYMENT: '/stripe/cancelPayment',
  GET_PAYMENT_STATUS: '/stripe/getPaymentStatus',
}

export const VOUCHER = {
  SEND_SMS_TO_CLIENT: '/voucher/sendSMSToClient',
  GET_ALL_VOUCHERS: '/voucher/getAllVouchers',
  ENABLE_VOUCHER: 'voucher/toggleVoucherEnable',
  ADD_VOUCHER: '/voucher/addNewVoucher',
  UPDATE_VOUCHER: '/voucher/updateVoucher',
  UPDATE_REDEEM_VOUCHER: '/voucher/redeemVoucher',
}

export const PROMOTIONS = { GET_PROMOTIONS: '/promotions/getPromotions' }

export const APP_SETTINGS = { GET_APP_SETTINGS: '/settings/getAppSettings' }

export const FEEDBACK = { GET_ALL_FEEDBACK: '/feedBack/getAllFeedback' }

export const PERMISSIONS = {
  GET_PERMISSIONS: '/permissions/getPermissions',
  UPDATE_PERMISSIONS: '/permissions/updatePermissions',
}

export const SETTINGS = {
  ENABLE_DISABLE_GIFT: '/settings/enableDisableGift?flag=',
  GET_FAQ_LIST: '/faq/getFaqList',
  ADD_NEW_FAQ: '/faq/addNewFaq',
  EDIT_FAQ: '/faq/editFaq',
  UPDATE_OWNER_SETTINGS: 'settings/updateOwnerSettings',
  GET_SETTINGS: 'settings/getSettings',
  GET_BUSINESS_HOURS: '/settings/getBusinessHours',
  UPDATE_BUSINESS_HOURS: '/settings/updateBusinessHours',
  SET_SLOT_DURATION: '/settings/setSlotsDuration',
}

export const NOTIFICATIONS = { UNREAD_COUNT: '/notification/unreadCount', MARK_READ: '/notification/markAsRead' }

export const SALES = { TRANSACTION_SUMMARY: 'dashboard/getDashboard' }
