// src/utils/axiosInstance.ts
import axios, { AxiosRequestConfig, AxiosError, AxiosInstance } from 'axios'
import Cookies from 'universal-cookie'

export const HOST_API = process.env.REACT_APP_API_URL

const cookies = new Cookies()

interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  __retryCount?: number
}

// Utility function to create an Axios instance with a given configuration
const createAxiosInstance = (config: ExtendedAxiosRequestConfig): AxiosInstance => {
  const axiosInstance = axios.create(config)
  applyRetryInterceptor(axiosInstance)
  return axiosInstance
}

// Centralized function to handle retry logic for Axios
const applyRetryInterceptor = (axiosInstance: AxiosInstance, maxRetries = 3, retryDelay = 5000) => {
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const config = error.config as ExtendedAxiosRequestConfig
      const { response } = error

      // Retry logic if the response status is 502 (Bad Gateway)
      if (response?.status === 502) {
        config.__retryCount = config.__retryCount || 0

        if (config.__retryCount < maxRetries) {
          config.__retryCount += 1
          await delay(retryDelay)
          return axiosInstance(config)
        }
      }

      return Promise.reject(error)
    },
  )
}

// Utility function for delaying the retry (helps with exponential backoff logic if needed)
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// Default configuration for API requests
const defaultFetcherConfig: ExtendedAxiosRequestConfig = {
  baseURL: `${HOST_API}/`,
  timeout: 1000 * 60 * 20,
  headers: { 'Content-Type': 'application/json;charset=UTF-8', 'X-XSRF-TOKEN': cookies.get('XSRF-TOKEN') },
}

// Example configuration for file upload requests
const uploadFetcherConfig: ExtendedAxiosRequestConfig = {
  baseURL: `${HOST_API}/`,
  timeout: 0,
  headers: { 'Content-Type': 'multipart/form-data; charset="utf-8";', 'X-XSRF-TOKEN': cookies.get('XSRF-TOKEN') },
}

// Example configuration for downloading files
const downloadFetcherConfig: ExtendedAxiosRequestConfig = {
  baseURL: `${HOST_API}/`,
  timeout: 0,
  responseType: 'blob',
  headers: { 'Content-Type': 'application/json;charset=UTF-8', 'X-XSRF-TOKEN': cookies.get('XSRF-TOKEN') },
}

// Example configuration for mock API requests
const mockFetcherConfig: ExtendedAxiosRequestConfig = {
  baseURL: '/api-mock',
  timeout: 1000 * 60 * 20,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'x-api-key': '****************************************************************',
  },
}

// Factory function to create the Axios instances based on type
const createFetcher = (type: 'default' | 'upload' | 'download' | 'mock'): AxiosInstance => {
  switch (type) {
    case 'upload':
      return createAxiosInstance(uploadFetcherConfig)
    case 'download':
      return createAxiosInstance(downloadFetcherConfig)
    case 'mock':
      return createAxiosInstance(mockFetcherConfig)
    default:
      return createAxiosInstance(defaultFetcherConfig)
  }
}

// Exporting different Axios instances based on the use case
export const fetcher = createFetcher('default')
export const uploadFetcher = createFetcher('upload')
export const downloadFetcher = createFetcher('download')
export const mockFetcher = createFetcher('mock')
