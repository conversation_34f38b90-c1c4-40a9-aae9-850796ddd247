export const convertSvgToPngBlob = (svgString: string, width: number, height: number): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(svgBlob)
    const img = new Image()

    img.onload = () => {
      const canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('Failed to get canvas context'))
        return
      }

      ctx.drawImage(img, 0, 0, width, height)
      canvas.toBlob((blob) => {
        URL.revokeObjectURL(url)
        if (blob) resolve(blob)
        else reject(new Error('Failed to convert canvas to blob'))
      }, 'image/png')
    }

    img.onerror = (e) => {
      URL.revokeObjectURL(url)
      reject(new Error(`Failed to load SVG image: ${e}`))
    }

    img.src = url
  })
}

// Convert image URL to base64
export const convertUrlToBase64 = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!imageUrl) {
      reject(new Error('No image URL provided'))
      return
    }

    const img = new Image()
    img.crossOrigin = 'Anonymous'

    const timeoutId = setTimeout(() => {
      reject(new Error(`Image load timed out for ${imageUrl}`))
    }, 10000)

    img.onload = () => {
      clearTimeout(timeoutId)
      try {
        const canvas = document.createElement('canvas')
        canvas.width = img.naturalWidth
        canvas.height = img.naturalHeight

        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Canvas context is null'))
          return
        }

        ctx.drawImage(img, 0, 0)
        const base64String = canvas.toDataURL('image/png').split(',')[1]
        resolve(base64String)
      } catch (e) {
        reject(e)
      }
    }

    img.onerror = (e) => {
      clearTimeout(timeoutId)
      reject(new Error(`Failed to load image from ${imageUrl}`))
    }

    img.src = imageUrl
  })
}
