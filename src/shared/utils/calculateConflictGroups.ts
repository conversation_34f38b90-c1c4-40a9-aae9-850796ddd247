import { isSameDay } from 'date-fns'

// Helper function to check if two appointments overlap
function isOverlapping(a: any, b: any): boolean {
  const startA = new Date(a.startTime).getTime()
  const endA = new Date(a.endTime).getTime()
  const startB = new Date(b.startTime).getTime()
  const endB = new Date(b.endTime).getTime()

  return startA < endB && startB < endA
}

export function calculateConflictGroups(bookingHours: any[], dates: Date | Date[]): Record<number, any> {
  // Normalize dates to an array and reset hours to start of day
  const targetDates = Array.isArray(dates) ? dates : [dates]
  const normalizedDates = targetDates.map((date) => {
    const newDate = new Date(date)
    newDate.setHours(0, 0, 0, 0)
    return newDate
  })

  // Group appointments by employee
  const appointmentsByEmployee: Record<number, any[]> = {}

  bookingHours.forEach((booking) => {
    if (!booking?.startTime || booking.employeeId == null) return

    const bookingDate = new Date(booking.startTime)
    bookingDate.setHours(0, 0, 0, 0)

    // Check if booking date matches any of our target dates
    const isDateMatch = normalizedDates.some((date) => date.getTime() === bookingDate.getTime())

    if (isDateMatch) {
      if (!appointmentsByEmployee[booking.employeeId]) {
        appointmentsByEmployee[booking.employeeId] = []
      }
      appointmentsByEmployee[booking.employeeId].push({ ...booking })
    }
  })

  // For each employee, group overlapping appointments and assign widths
  const conflictGroups: Record<number, any> = {}

  Object.keys(appointmentsByEmployee).forEach((employeeIdStr) => {
    const employeeId = +employeeIdStr
    const appointments = appointmentsByEmployee[employeeId]

    if (!appointments || !appointments.length) return

    // Sort appointments by start time
    appointments.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())

    let groupId = 0
    const visited = new Set()
    const groups: any[] = []

    for (let i = 0; i < appointments.length; i++) {
      const current = appointments[i]
      if (visited.has(current.id)) continue

      const group: any[] = [current]
      visited.add(current.id)

      let groupStart = new Date(current.startTime).getTime()
      let groupEnd = new Date(current.endTime).getTime()

      for (let j = i + 1; j < appointments.length; j++) {
        const compare = appointments[j]
        if (visited.has(compare.id)) continue

        // Only check for overlap if they're on the same day
        if (
          isSameDay(new Date(current.startTime), new Date(compare.startTime)) &&
          group.some((appt) => isOverlapping(appt, compare))
        ) {
          group.push(compare)
          visited.add(compare.id)

          // Extend the time range
          const compareStart = new Date(compare.startTime).getTime()
          const compareEnd = new Date(compare.endTime).getTime()
          groupStart = Math.min(groupStart, compareStart)
          groupEnd = Math.max(groupEnd, compareEnd)
        }
      }

      // Assign width and position to each appointment in the group
      group.forEach((appt, idx) => {
        appt.conflictGroupId = groupId
        appt.width = 100 / group.length
        appt.position = idx
      })

      groups.push({
        groupId,
        appointments: group,
        start: groupStart,
        end: groupEnd,
      })

      groupId++
    }

    conflictGroups[employeeId] = groups
  })

  return conflictGroups
}
