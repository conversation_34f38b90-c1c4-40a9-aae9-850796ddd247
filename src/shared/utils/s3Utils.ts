import { S3 } from 'aws-sdk'

export const uploadFileToS3 = async (
  fileBlob: Blob,
  fileName: string,
  fileExtension: string = 'png',
  from: string,
): Promise<string> => {
  const s3 = new S3({
    accessKeyId: process.env.REACT_APP_AWS_ACCESS_KEY,
    secretAccessKey: process.env.REACT_APP_AWS_SECRET_KEY,
    region: process.env.REACT_APP_AWS_REGION,
  })

  const uniqueKey = `${from}/${fileName}_${Date.now()}.${fileExtension}`
  const contentType = fileExtension === 'svg' ? 'image/svg+xml' : 'image/png'

  const params: S3.PutObjectRequest = {
    Bucket: process.env.REACT_APP_AWS_S3_BUCKET_NAME!,
    Key: uniqueKey,
    Body: fileBlob,
    ACL: 'public-read',
    ContentType: contentType,
  }

  try {
    await s3.putObject(params).promise()
    return uniqueKey
  } catch (err) {
    console.error('S3 Upload Error:', err)
    throw new Error('Failed to upload to S3')
  }
}
