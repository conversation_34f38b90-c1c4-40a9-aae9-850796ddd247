import { updateClientProfileActiveTab, updateSelectedAppointmentDate, updateSelectedWeekDate } from '.'
import { useAppDispatch, useAppSelector } from '@/redux/hooks/hook'

const useGlobalSliceActions = () => {
  const dispatch = useAppDispatch()

  const { selectedAppointmentDate, selectedWeekDate, clientProfileActiveTab } = useAppSelector((state) => state.global)
  const setSelectedAppointmentDate = (payload: Date) => dispatch(updateSelectedAppointmentDate(payload))
  const setSelectedWeekDate = (payload: Date) => dispatch(updateSelectedWeekDate(payload))
  const setClientProfileActiveTab = (payload: number | null) => dispatch(updateClientProfileActiveTab(payload))

  return {
    selectedWeekDate,
    selectedAppointmentDate,
    clientProfileActiveTab,
    setSelectedAppointmentDate,
    setClientProfileActiveTab,
    setSelectedWeekDate,
  }
}

export default useGlobalSliceActions
