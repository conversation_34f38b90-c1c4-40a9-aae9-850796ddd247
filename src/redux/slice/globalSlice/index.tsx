import { createSlice } from '@reduxjs/toolkit'
import { InitialGlobalState } from './interface'

const initialState: InitialGlobalState = {
  selectedAppointmentDate: new Date(),
  clientProfileActiveTab: 1,
  selectedWeekDate: new Date(),
}

const globalSlice = createSlice({
  name: 'global',
  initialState,
  reducers: {
    updateSelectedAppointmentDate(state, action) {
      state.selectedAppointmentDate = action?.payload
    },
    updateClientProfileActiveTab(state, action) {
      state.clientProfileActiveTab = action?.payload
    },
    updateSelectedWeekDate(state, action) {
      state.selectedWeekDate = action?.payload
    },
  },
})

export const { updateSelectedAppointmentDate, updateClientProfileActiveTab, updateSelectedWeekDate } =
  globalSlice.actions
export default globalSlice.reducer
