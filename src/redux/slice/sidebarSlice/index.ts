import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { SidebarState } from './interface'

const initialState: SidebarState = {
  isExpanded: true,
}

const sidebarSlice = createSlice({
  name: 'sidebar',
  initialState,
  reducers: {
    setIsExpanded(state, action: PayloadAction<boolean>) {
      state.isExpanded = action.payload
    },
    toggleIsExpanded(state) {
      state.isExpanded = !state.isExpanded
    },
  },
})

export const { setIsExpanded, toggleIsExpanded } = sidebarSlice.actions
export default sidebarSlice.reducer
