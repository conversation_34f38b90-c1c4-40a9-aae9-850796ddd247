import { useSelector, useDispatch } from 'react-redux'
import { setIsExpanded, toggleIsExpanded } from './index'
import { RootState } from '@/redux/store'

export const useSidebarSliceAction = () => {
  const dispatch = useDispatch()
  const isExpanded = useSelector((state: RootState) => state.sidebar.isExpanded)

  const updateIsExpanded = (value: boolean) => {
    dispatch(setIsExpanded(value))
  }

  const toggleSidebar = () => {
    dispatch(toggleIsExpanded())
  }

  return {
    isExpanded,
    updateIsExpanded,
    toggleSidebar,
  }
}
