import { IAppointmentIndexes, IGenerateAppointment, ISlot } from '@/features/appointments/components/calender/interface'
import { IAppointment } from '@/shared/hooks/apis/getAllBookings'
import { IStaffDetails } from '@/shared/hooks/apis/getAllStaff'
import { IService } from '@/shared/hooks/apis/services/interface'
import { IVoucherItem } from '@/shared/hooks/apis/updateVoucher/apis'

export interface ProviderDetails {
  label: string
  value: string
  img: string
}

export interface ISelectedServices extends IService {
  serviceNumber?: number
  startTime?: string
  endTime?: string
  teamMember?: string
  isNew?: boolean
  bookingId?: number
  providerDetails?: ProviderDetails
}

export interface InitialCalendarState {
  currentTime: string
  currentPage: number
  todayTimeSlots: string[]
  selectProfessional: number | null
  bookingHours: IAppointment[]
  isWeekView: boolean
  staffs: IStaffDetails[]
  selectedSlot: ISlot | null //use for slot drawer
  isLoading: boolean
  selectedMinute: null | Date
  selectedService: ISelectedServices | null
  selectedServices: ISelectedServices[]
  showLabelRow: boolean
  userFirstAppointment: Record<number, IAppointment> | null
  selectedAppointmentProvider: IGenerateAppointment | null
  showRescheduleWarning: boolean
  resendFormData: ISlot | null
  checkoutServices: ISelectedServices[]
  lastBookSlot: ISelectedServices[] | ISlot[] | IGenerateAppointment | any
  selectedGiftVoucher: IVoucherItem | null
}
