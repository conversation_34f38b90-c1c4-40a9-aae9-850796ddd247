import { format, parse } from 'date-fns'
import {
  updateCurrentTime,
  updateCurrentPage,
  updateTodayTimeSlots,
  updateSelectProfessional,
  updateBookingHours,
  updateIsWeekView,
  updateStaffs,
  updateSelectedSlot,
  updateIsLoading,
  updateSelectedMinute,
  updateSelectedService,
  updateSelectedServices,
  updateShowLabelRow,
  updateUserFirstAppointment,
  updateSelectedAppointmentProvider,
  updateShowRescheduleWarning,
  updateResendAllFormData,
  updateCheckoutServices,
  updateLastBookSlot,
  updateSelectedGiftVoucher,
} from '.'
import { IGenerateAppointment, ISlot } from '@/features/appointments/components/calender/interface'
import { useAppDispatch, useAppSelector } from '@/redux/hooks/hook'
import { BOOKINGS } from '@/shared/constant/apiEndpoints'
import { IAppointment } from '@/shared/hooks/apis/getAllBookings'
import { IStaffDetails } from '@/shared/hooks/apis/getAllStaff'
import { fetcher } from '@/shared/utils/api/axiosInstance'

const useCalendarSliceAction = () => {
  const dispatch = useAppDispatch()

  const {
    currentTime,
    currentPage,
    todayTimeSlots,
    selectProfessional,
    bookingHours,
    isWeekView,
    staffs,
    selectedSlot,
    isLoading,
    selectedMinute,
    selectedService,
    selectedServices,
    showLabelRow,
    userFirstAppointment,
    selectedAppointmentProvider,
    showRescheduleWarning,
    resendFormData,
    checkoutServices,
    lastBookSlot,
    selectedGiftVoucher,
  } = useAppSelector((state) => state.calender)

  const setCurrentTime = (payload: string) => dispatch(updateCurrentTime(payload))
  const setCurrentPage = (payload: number) => dispatch(updateCurrentPage(payload))
  const setTodayTimeSlots = (payload: string[]) => dispatch(updateTodayTimeSlots(payload))
  const setSelectProfessional = (payload: number | null) => dispatch(updateSelectProfessional(payload))
  const setBookingHours = (payload: IAppointment[]) => dispatch(updateBookingHours(payload))
  const setWeekView = (payload: boolean) => dispatch(updateIsWeekView(payload))
  const setStaffs = (payload: IStaffDetails[]) => dispatch(updateStaffs(payload))
  const setSelectedSlot = (payload: any) => dispatch(updateSelectedSlot(payload))
  const setIsLoading = (payload: any) => dispatch(updateIsLoading(payload))
  const setSelectedMinute = (payload: any) => dispatch(updateSelectedMinute(payload))
  const setSelectedService = (payload: any) => dispatch(updateSelectedService(payload))
  const setSelectedServices = (payload: any) => dispatch(updateSelectedServices(payload))
  const setShowLabelRow = (payload: boolean) => dispatch(updateShowLabelRow(payload))
  const setUserFirstAppointment = (payload: Record<number, IAppointment>) =>
    dispatch(updateUserFirstAppointment(payload))
  const setSelectedAppointmentProvider = (payload: IGenerateAppointment | null) =>
    dispatch(updateSelectedAppointmentProvider(payload))
  const setShowRescheduleWarning = (payload: boolean) => dispatch(updateShowRescheduleWarning(payload))
  const setResendAllFormData = (payload: ISlot | null) => dispatch(updateResendAllFormData(payload))
  const setSelectedCheckoutServices = (payload: any) => dispatch(updateCheckoutServices(payload))
  const setLastBookSlot = (payload: any) => dispatch(updateLastBookSlot(payload))
  const setSelectedGiftVoucher = (payload: any) => dispatch(updateSelectedGiftVoucher(payload))

  const onMobileFabAdd = (referenceDate: Date) => {
    const initialDate = parse(format(new Date(), 'HH:mm'), 'HH:mm', new Date(referenceDate))
    setSelectedSlot(null)
    setSelectedMinute(initialDate)
    setShowLabelRow(false)
    setSelectedServices([])
  }

  const onMobileFabAddClose = () => {
    setSelectedService(null)
    setSelectedServices([])
  }

  const updateCalenderAppointments = async () => {
    const res = await fetcher.get(`${BOOKINGS.GET_ALL_BOOKINGS}`)
    setBookingHours(res?.data)
  }

  return {
    currentTime,
    currentPage,
    todayTimeSlots,
    selectProfessional,
    bookingHours,
    isWeekView,
    staffs,
    selectedSlot,
    isLoading,
    selectedMinute,
    selectedService,
    selectedServices,
    showLabelRow,
    userFirstAppointment,
    selectedAppointmentProvider,
    setCurrentTime,
    setCurrentPage,
    setTodayTimeSlots,
    setBookingHours,
    setSelectProfessional,
    setWeekView,
    setStaffs,
    setSelectedSlot,
    setIsLoading,
    setSelectedMinute,
    setSelectedService,
    setSelectedServices,
    setShowLabelRow,
    setUserFirstAppointment,
    setSelectedAppointmentProvider,
    onMobileFabAdd,
    onMobileFabAddClose,
    updateCalenderAppointments,
    showRescheduleWarning,
    setShowRescheduleWarning,
    resendFormData,
    setResendAllFormData,
    checkoutServices,
    setSelectedCheckoutServices,
    lastBookSlot,
    setLastBookSlot,
    selectedGiftVoucher,
    setSelectedGiftVoucher,
  }
}

export default useCalendarSliceAction
