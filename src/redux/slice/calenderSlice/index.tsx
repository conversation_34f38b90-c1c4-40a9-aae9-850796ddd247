import { createSlice } from '@reduxjs/toolkit'
import { InitialCalendarState } from './interface'

const initialState: InitialCalendarState = {
  currentTime: '12:00',
  currentPage: 0,
  todayTimeSlots: [],
  selectProfessional: null,
  bookingHours: [],
  isWeekView: false,
  staffs: [],
  selectedSlot: null,
  isLoading: false,
  selectedMinute: null,
  selectedService: null,
  selectedServices: [],
  showLabelRow: false,
  userFirstAppointment: null,
  selectedAppointmentProvider: null,
  showRescheduleWarning: false,
  resendFormData: null,
  checkoutServices: [],
  lastBookSlot: [],
  selectedGiftVoucher: null,
}

const calenderSlice = createSlice({
  name: 'calender',
  initialState,
  reducers: {
    updateCurrentTime(state, action) {
      state.currentTime = action?.payload
    },
    updateCurrentPage(state, action) {
      state.currentPage = action?.payload
    },
    updateTodayTimeSlots(state, action) {
      state.todayTimeSlots = action?.payload
    },
    updateSelectProfessional(state, action) {
      state.selectProfessional = action?.payload
    },
    updateBookingHours(state, action) {
      state.bookingHours = action?.payload
    },
    updateIsWeekView(state, action) {
      state.isWeekView = action?.payload
    },
    updateStaffs(state, action) {
      state.staffs = action?.payload
    },
    updateSelectedSlot(state, action) {
      state.selectedSlot = action?.payload
    },
    updateIsLoading(state, action) {
      state.isLoading = action?.payload
    },
    updateSelectedMinute(state, action) {
      state.selectedMinute = action?.payload
    },
    updateSelectedService(state, action) {
      state.selectedService = action?.payload
    },
    updateSelectedServices(state, action) {
      state.selectedServices = action?.payload
    },
    updateShowLabelRow(state, action) {
      state.showLabelRow = action?.payload
    },
    updateUserFirstAppointment(state, action) {
      state.userFirstAppointment = action?.payload
    },
    updateSelectedAppointmentProvider(state, action) {
      state.selectedAppointmentProvider = action?.payload
    },
    updateShowRescheduleWarning(state, action) {
      state.showRescheduleWarning = action?.payload
    },
    updateResendAllFormData(state, action) {
      state.resendFormData = action?.payload
    },
    updateCheckoutServices(state, action) {
      state.checkoutServices = action?.payload
    },
    updateLastBookSlot(state, action) {
      state.lastBookSlot = action?.payload
    },
    updateSelectedGiftVoucher(state, action) {
      state.selectedGiftVoucher = action?.payload
    },
  },
})

export const {
  updateCurrentTime,
  updateCurrentPage,
  updateTodayTimeSlots,
  updateSelectProfessional,
  updateBookingHours,
  updateIsWeekView,
  updateStaffs,
  updateSelectedSlot,
  updateIsLoading,
  updateSelectedMinute,
  updateSelectedService,
  updateSelectedServices,
  updateShowLabelRow,
  updateUserFirstAppointment,
  updateSelectedAppointmentProvider,
  updateShowRescheduleWarning,
  updateResendAllFormData,
  updateCheckoutServices,
  updateLastBookSlot,
  updateSelectedGiftVoucher,
} = calenderSlice.actions
export default calenderSlice.reducer
