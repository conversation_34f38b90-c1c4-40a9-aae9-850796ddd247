import { setFinalVoucher, setAppliedCode, setDiscountAmount, setVoucherOpen, clearVoucher } from '.'
import { VoucherHookConfig } from './interface'
import { useAppDispatch, useAppSelector } from '@/redux/hooks/hook'

const useVoucherSliceAction = (config: VoucherHookConfig = {}) => {
  const dispatch = useAppDispatch()

  const { stateSelector = (state: any) => state.voucher } = config

  const { finalVoucher, appliedCode, discountAmount, voucherOpen } = useAppSelector(stateSelector)

  const applyVoucher = (amount: number, code: string, discount: number, additionalData?: any) => {
    dispatch(setFinalVoucher(amount))
    dispatch(setAppliedCode(code))
    dispatch(setDiscountAmount(discount))

    if (additionalData && config.additionalOptions?.handleAdditionalData) {
      config.additionalOptions.handleAdditionalData(dispatch, additionalData)
    }
  }

  const removeVoucher = () => {
    dispatch(clearVoucher())
  }

  const toggleVoucherOpen = (isOpen: boolean) => {
    dispatch(setVoucherOpen(isOpen))
  }

  return {
    finalVoucher,
    appliedCode,
    discountAmount,
    voucherOpen,
    applyVoucher,
    removeVoucher,
    toggleVoucherOpen,
  }
}

export default useVoucherSliceAction
