import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { VoucherState } from './interface'

const initialState: VoucherState = {
  finalVoucher: null,
  appliedCode: null,
  discountAmount: 0,
  voucherOpen: false,
}

const voucherSlice = createSlice({
  name: 'voucher',
  initialState,
  reducers: {
    setFinalVoucher(state, action: PayloadAction<number | null>) {
      state.finalVoucher = action.payload
    },
    setAppliedCode(state, action: PayloadAction<string | null>) {
      state.appliedCode = action.payload
    },
    setDiscountAmount(state, action: PayloadAction<number>) {
      state.discountAmount = action.payload
    },
    setVoucherOpen(state, action: PayloadAction<boolean>) {
      state.voucherOpen = action.payload
    },
    clearVoucher(state) {
      state.finalVoucher = null
      state.appliedCode = null
      state.discountAmount = 0
    },
  },
})

export const { setFinalVoucher, setAppliedCode, setDiscountAmount, setVoucherOpen, clearVoucher } = voucherSlice.actions

export default voucherSlice.reducer
