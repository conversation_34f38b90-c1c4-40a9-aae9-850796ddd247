import { createSlice } from '@reduxjs/toolkit'
import { InitialStaffLoginState } from './interface'

const initialState: InitialStaffLoginState = {
  staffLoginDetails: null,
}

const staffLoginSlice = createSlice({
  name: 'staffLogin',
  initialState,
  reducers: {
    updateStaffLoginDetails(state, action) {
      state.staffLoginDetails = action?.payload
    },
  },
})

export const { updateStaffLoginDetails } = staffLoginSlice.actions
export default staffLoginSlice.reducer
