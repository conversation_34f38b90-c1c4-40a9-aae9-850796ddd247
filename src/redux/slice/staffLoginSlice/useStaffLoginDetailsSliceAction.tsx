import { updateStaffLoginDetails } from '.'
import { useAppDispatch, useAppSelector } from '@/redux/hooks/hook'

const useStaffLoginDetailsSliceAction = () => {
  const dispatch = useAppDispatch()

  const { staffLoginDetails } = useAppSelector((state) => state.staffLogin)

  const setStaffLoginDetails = (payload: any) => dispatch(updateStaffLoginDetails(payload))

  return {
    staffLoginDetails,
    setStaffLoginDetails,
  }
}

export default useStaffLoginDetailsSliceAction
