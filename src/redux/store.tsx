import { configureStore } from '@reduxjs/toolkit'
import calenderReducer from '@/redux/slice/calenderSlice'
import globalReducer from '@/redux/slice/globalSlice'
import sidebarReducer from '@/redux/slice/sidebarSlice'
import staffLoginReducer from '@/redux/slice/staffLoginSlice'
import voucherReducer from '@/redux/slice/voucherSlice'

export const store = configureStore({
  reducer: {
    global: globalReducer,
    calender: calenderReducer,
    voucher: voucherReducer,
    staffLogin: staffLoginReducer,
    sidebar: sidebarReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
