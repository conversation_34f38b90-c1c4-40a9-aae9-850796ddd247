import { TableContainer } from '@mui/material'
import MUIList from '@/shared/components/MUIList'
import MUIStack from '@/shared/components/MUIStack'
import { MUIStyled } from '@/shared/components/MUIStyled'
import MUITypography from '@/shared/components/MUITypography'

export const RotaTable = MUIStyled(MUIStack)(({ theme }) => ({
  border: `1px solid ${theme.palette.lightGray?.main}`,
  borderRadius: 16,
  overflowX: 'auto',
  '& .MuiTable-root': {
    '& .MuiTableHead-root': {
      '& .MuiTableCell-root': {
        fontSize: 18,
        padding: '20px 10px',
        minWidth: 140,
        width: 140,
        color: `${theme.palette.darkGray?.light}66`,
        backgroundColor: theme.palette.white?.main,
        textAlign: 'center',
        '&:first-child': {
          position: 'sticky',
          left: 0,
          zIndex: 9,
          width: 100,
          minWidth: 100,
        },
      },
    },
    '& .MuiTableBody-root': {
      '& .MuiTableRow-root': {
        '& .MuiTableCell-root': {
          textAlign: 'center',
          padding: 10,
          minWidth: 140,
          width: 140,
          backgroundColor: theme.palette.white?.main,
          '&:not(:last-child), &:not(:first-child)': {
            verticalAlign: 'top',
          },
          '&:first-child': {
            position: 'sticky',
            left: 0,
            zIndex: 9,
            width: 100,
            minWidth: 100,
          },
          '& .MuiFormControl-root': {
            marginBottom: 0,
            '& .MuiInputBase-root': {
              backgroundColor: `${theme.palette.lightGray?.light}0A`,
              '& input': {
                textAlign: 'center',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                border: 0,
              },
            },
          },
          '& .MuiButtonBase-root': {
            '&.add-btn': {
              borderColor: theme.palette.gray?.main,
              color: theme.palette.gray?.light,
              fontSize: 18,
              padding: 3,
              minHeight: 'auto',
            },
            '&.trainingday-btn': {
              backgroundColor: theme.palette.orange?.main,
            },
            '&.break-btn': {
              backgroundColor: theme.palette.natural?.main,
            },
            '&.sickday-btn': {
              backgroundColor: theme.palette.success?.main,
            },
            '&.annualleave-btn': {
              backgroundColor: theme.palette.danger?.main,
            },
          },
        },
        '&:last-child': {
          '& .MuiTableCell-root': {
            borderBottom: 0,
          },
        },
      },
    },
  },
}))

export const TotalHours = MUIStyled(MUIStack)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  borderRadius: 12,
  textAlign: 'left',
  padding: '18px 24px',
  [theme.breakpoints.down('lg')]: {
    padding: '10px',
  },
  '& p': {
    fontWeight: 500,
    color: theme.palette.gray?.light,
  },
}))

export const SiftHour = MUIStyled(MUITypography)(({ theme }) => ({
  fontSize: 24,
  border: `1px solid ${theme.palette.gray?.main}`,
  padding: '22px 28px',
  borderRadius: 16,
  [theme.breakpoints.down('xxs')]: {
    padding: '20px 14px',
    fontSize: 18,
  },
  '& i': {
    float: 'right',
    color: `${theme.palette.darkGray?.light}66`,
    transform: 'rotate(90deg)',
    fontWeight: 'normal',
  },
}))
export const ExcuseList = MUIStyled(MUIList)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  [theme.breakpoints.down('xxs')]: {
    justifyContent: 'space-between',
  },
  '& .MuiListItem-root': {
    width: '50%',
    padding: 8,
    [theme.breakpoints.down('xxs')]: {
      padding: '20px 0px',
      width: 'calc(50% - 8px)',
    },
  },
}))
