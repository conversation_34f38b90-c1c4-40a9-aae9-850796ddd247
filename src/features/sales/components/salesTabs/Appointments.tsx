import React, { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'
import { IconButton, InputAdornment, TableContainer } from '@mui/material'
import {
  addDays,
  differenceInDays,
  differenceInMinutes,
  endOfWeek,
  format,
  startOfToday,
  startOfWeek,
  subDays,
} from 'date-fns'
import { debounce } from 'lodash'
import { DateSelection, SalesTable } from './Sales.style'
import usenewSearchDebounce from '../debounceSearch'
import { StyledWebDateRangePicker } from '@/features/appointments/pages/appointments/Appointments.style'

import { FilterRight } from '@/layout/Layout.style'
import DataTable from '@/shared/components/CommonTable'
import { IDropdownOption } from '@/shared/components/dropDown/interface'
import M<PERSON><PERSON>ox from '@/shared/components/MUIBox'
import MUIButton from '@/shared/components/MUIButton'
import MUIMenu from '@/shared/components/MUIMenu'
import MUIMenuI<PERSON> from '@/shared/components/MUIMenuItem'
import M<PERSON><PERSON>tack from '@/shared/components/MUIStack'
import MUITable from '@/shared/components/MUITable'
import MUITableBody from '@/shared/components/MUITableBody'
import MUITableCell from '@/shared/components/MUITableCell'
import MUITableHead from '@/shared/components/MUITableHead'
import MUITableRow from '@/shared/components/MUITableRow'
import MUITextField from '@/shared/components/MUITextField'
import MUITypography from '@/shared/components/MUITypography'
import { useGetAllRetrieveBookings } from '@/shared/hooks/apis/getAllRetrieveBookings'
import { useGetAllForReport } from '@/shared/hooks/apis/getAllStaffForReport'
import useSearchDebounce from '@/shared/hooks/useSearchDebounce'

type DateFilterProps = {
  dateRange: any // You can replace 'any' with a specific type like Date[] if applicable
  setDateRange: Dispatch<SetStateAction<any>>
  showDatePlaceholder: boolean
  handlePreviousWeek: () => void
  handleNextWeek: () => void
  handleQuickAction: (event: React.MouseEvent<HTMLElement>) => void
  selectedFilter: string
}

const DateFilter: React.FC<DateFilterProps> = ({
  dateRange,
  setDateRange,
  showDatePlaceholder,
  handlePreviousWeek,
  handleNextWeek,
  handleQuickAction,
  selectedFilter,
}) => {
  return (
    <FilterRight
      spacing={2}
      ml={{ md: 2 }}
      mb={{ md: 0, xs: 2 }}
      flex="none"
      direction="row"
      alignItems="center"
      justifyContent="space-between"
    >
      <DateSelection
        direction="row"
        alignItems="center"
        sx={{
          '.MuiMultiInputDateRangeField-root': {
            maxWidth: '294px',
          },
        }}
      >
        <i className="ri-arrow-left-s-line" onClick={handlePreviousWeek}></i>
        <StyledWebDateRangePicker
          value={dateRange}
          onAccept={setDateRange}
          format="dd/MM/yyyy"
          showPlaceholder={showDatePlaceholder}
        />
        <i className="ri-arrow-right-s-line" onClick={handleNextWeek}></i>
      </DateSelection>
      <IconButton onClick={handleQuickAction} sx={{ display: { xsm: 'none', xs: 'block' } }}>
        <i className="ri-filter-line"></i>
      </IconButton>
      <MUIButton
        variant="outlined"
        color="secondary"
        onClick={handleQuickAction}
        sx={{ display: { xsm: 'block', xs: 'none' } }}
      >
        {selectedFilter !== 'All' ? selectedFilter : 'Filters'} <i className="ri-filter-line"></i>
      </MUIButton>
    </FilterRight>
  )
}

const Appointments: () => React.JSX.Element = () => {
  const { data: allRetrieveBookings, mutate: fetchAllRetrieveBookings, isLoading } = useGetAllRetrieveBookings()

  useEffect(() => {
    fetchAllRetrieveBookings()
  }, [])
  const { data: staff } = useGetAllForReport()

  const [selectedFilter, setSelectedFilter] = useState<string>('All')
  // const [searchTerm, setSearchTerm] = useState<string>('')
  const [quickActions, setQuickAction] = useState<HTMLElement | null>(null)
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])
  const [showDatePlaceholder, setShowDatePlaceholder] = useState<boolean>(true)

  const [searchQuery, setSearchQuery] = useState<string>('')
  const [deBounceValue, setDeBounceValue] = useState<string>('')

  const staffMap = useMemo(() => {
    const map: Record<number, string> = {}
    if (staff) {
      staff.forEach((employee) => {
        if (employee.employeeId !== undefined) {
          map[employee.employeeId] = `${employee.firstName} ${employee.lastName}`.trim() // Combine first & last name
        }
      })
    }

    return map
  }, [staff])

  const filteredBookings = useMemo(() => {
    if (!allRetrieveBookings) return []

    const now = new Date()

    const filtered = allRetrieveBookings.filter((booking) => {
      const searchLower = searchQuery.toLowerCase().trim()
      const fullName = `${booking?.firstName} ${booking?.lastName}`.toLowerCase()
      const matchesSearch =
        fullName.includes(searchLower) ||
        booking?.phoneNumber?.toLowerCase().includes(searchLower) ||
        booking?.service?.toLowerCase().includes(searchLower)

      const startTime = booking.startTime ? new Date(booking.startTime) : null
      const endTime = booking.endTime ? new Date(booking.endTime) : null

      // Handle optional dateRange
      const startOfRange = dateRange?.[0] ? new Date(dateRange[0]) : null
      const endOfRange = dateRange?.[1] ? new Date(dateRange[1]) : null

      if (endOfRange) {
        endOfRange.setHours(23, 59, 59, 999) // Extend to end of the day
      }

      const matchesDateRange =
        !startOfRange ||
        !endOfRange || // If no date range is selected, return true
        (startTime &&
          ((startTime >= startOfRange && startTime <= endOfRange) || // Start within range
            (endTime && endTime >= startOfRange && endTime <= endOfRange) || // End within range
            (startTime <= startOfRange && endTime && endTime >= endOfRange))) // Enclosing range

      if (selectedFilter === 'No Show') {
        return matchesSearch && booking.noShow && matchesDateRange
      } else if (selectedFilter === 'Future Booking') {
        return matchesSearch && startTime && startTime > now && matchesDateRange
      } else if (selectedFilter === 'Canceled') {
        return matchesSearch && booking.cancelled !== false && matchesDateRange
      }

      return matchesSearch && matchesDateRange
    })

    return filtered.sort((a, b) => {
      const dateA = a.startTime ? new Date(a.startTime).getTime() : 0
      const dateB = b.startTime ? new Date(b.startTime).getTime() : 0
      return dateB - dateA
    })
  }, [allRetrieveBookings, selectedFilter, dateRange, searchQuery])

  const formattedDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM, h:mm a') // Example: 15 Jan, 3:30 PM
  }

  const getDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const minutes = differenceInMinutes(end, start)

    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60

    if (hours > 0 && remainingMinutes > 0) {
      return `${hours}h ${remainingMinutes}min`
    } else if (hours > 0) {
      return `${hours}h`
    } else {
      return `${remainingMinutes}min`
    }
  }

  const handleOptionSelect = (option: IDropdownOption) => {}

  const handleQuickClose = () => {
    setQuickAction(null)
  }

  const handleQuickAction = (event: React.MouseEvent<HTMLElement>) => {
    setQuickAction(event.currentTarget)
  }

  const handleFilterSelect = (filter: string) => {
    setSelectedFilter(filter)
    handleQuickClose() // Close the filter menu after selection
  }

  const getMenuItemStyle = (filter: string) => {
    return selectedFilter === filter
      ? { backgroundColor: '#E0E0E0' } // Change this color to match your theme or preference
      : {}
  }

  const handleNextWeek = () => {
    if (dateRange?.length > 0) {
      const differenceDays = differenceInDays(dateRange[1] as Date, dateRange[0] as Date) + 1
      setDateRange([addDays(dateRange[0] as Date, differenceDays), addDays(dateRange[1] as Date, differenceDays)])
      setShowDatePlaceholder(false)
    }
  }

  const handlePreviousWeek = () => {
    if (dateRange?.length > 0) {
      const differenceDays = differenceInDays(dateRange[1] as Date, dateRange[0] as Date) + 1
      setDateRange([subDays(dateRange[0] as Date, differenceDays), subDays(dateRange[1] as Date, differenceDays)])
      setShowDatePlaceholder(false)
    }
  }

  useEffect(() => {
    const today = startOfToday()
    const startOfWeekDate = startOfWeek(today, { weekStartsOn: 1 })
    const endOfWeekDate = endOfWeek(today, { weekStartsOn: 1 })

    setDateRange([startOfWeekDate, endOfWeekDate])
  }, [])

  useEffect(() => {
    if (dateRange && !(dateRange[0] === null && dateRange[1] === null)) {
      setShowDatePlaceholder(false)
    }
  }, [dateRange])

  const columns = [
    {
      accessorKey: 'id',
      header: 'Ref #',
      cell: ({ row }: any) => {
        const client = row.original
        return <span style={{ color: '#924FE8', fontWeight: 500 }}>{client.id}</span>
      },
    },
    {
      accessorKey: 'employeeName',
      header: 'Client',
      cell: ({ row }: any) => {
        const client = row.original
        return <span style={{ color: '#924FE8', fontWeight: 500 }}>{client.employeeName}</span>
      },
    },

    { accessorKey: 'service', header: 'Service' },
    {
      accessorKey: 'startTime',
      header: 'Scheduled Date',
      cell: ({ row }: any) => {
        const client = row.original
        return client.startTime ? formattedDate(client.startTime) : '-'
      },
    },
    {
      accessorKey: 'endTime',
      header: 'Duration',
      cell: ({ row }: any) => {
        const client = row.original
        return client.startTime && client.endTime ? getDuration(client.startTime, client.endTime) : '-'
      },
    },
    {
      accessorKey: 'date',
      header: 'Team member',
      cell: ({ row }: any) => {
        const client = row.original
        return client.employeeId !== undefined ? (staffMap[client.employeeId] ?? '-') : '-'
      },
    },
    {
      accessorKey: 'pendingPrice',
      header: 'Price',
      cell: ({ row }: any) => {
        const client = row.original
        return `€${client.pendingPrice}`
      },
    },
  ]

  const debouncedRequest = useSearchDebounce(() => {
    // onSearch(searchText);
    setSearchQuery(deBounceValue)
  })
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDeBounceValue(event.target.value.toLowerCase())
    debouncedRequest()
  }

  return (
    <>
      <MUIStack textAlign="left">
        <MUIStack sx={{ display: { md: 'none', xs: 'block' } }}>
          <DateFilter
            dateRange={dateRange}
            setDateRange={setDateRange}
            showDatePlaceholder={showDatePlaceholder}
            handlePreviousWeek={handlePreviousWeek}
            handleNextWeek={handleNextWeek}
            handleQuickAction={handleQuickAction}
            selectedFilter={selectedFilter}
          />
        </MUIStack>
        <MUITypography variant="h5" fontWeight="bold" mb={0.5}>
          Appointments
        </MUITypography>
        <MUITypography color="lightGray.light200">
          View, filter and export appointments booked by your clients.
        </MUITypography>
        <MUIStack mt={{ sm: 3.5, xs: 2.5 }} mb={{ sm: 3.5, xs: 1 }} direction="row" justifyContent="space-between">
          <MUIStack maxWidth={360} width="100%">
            <MUITextField
              value={deBounceValue}
              // setSearchTerm(e.target.value)
              onChange={handleSearchChange}
              placeholder="Search by client name and service"
              fullWidth
              variant="outlined"
              size="small"
              formControlSx={{ mb: 0 }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  padding: '2px 7px !important',
                  borderRadius: 30,
                },
              }}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">{<i className="ri-search-line"></i>}</InputAdornment>
                  ),
                },
              }}
            />
          </MUIStack>
          <MUIStack sx={{ display: { md: 'block', xs: 'none' } }}>
            <DateFilter
              dateRange={dateRange}
              setDateRange={setDateRange}
              showDatePlaceholder={showDatePlaceholder}
              handlePreviousWeek={handlePreviousWeek}
              handleNextWeek={handleNextWeek}
              handleQuickAction={handleQuickAction}
              selectedFilter={selectedFilter}
            />
          </MUIStack>
        </MUIStack>
        <SalesTable>
          {/* <TableContainer>
            <MUITable>
              <MUITableHead>
                <MUITableRow>
                  <MUITableCell>Ref #</MUITableCell>
                  <MUITableCell>Client</MUITableCell>
                  <MUITableCell>Service</MUITableCell>
                  <MUITableCell>Scheduled Date</MUITableCell>
                  <MUITableCell>Duration</MUITableCell>
                  <MUITableCell>Team member</MUITableCell>
                  <MUITableCell>Price</MUITableCell>
                </MUITableRow>
              </MUITableHead>
              <MUITableBody>
                {filteredBookings?.map((booking, i) => {
                  return (
                    <MUITableRow key={i}>
                      <MUITableCell>
                        <MUITypography component="span">{booking.id}</MUITypography>
                      </MUITableCell>
                      <MUITableCell>
                        <MUITypography component="span">{booking.employeeName}</MUITypography>
                      </MUITableCell>
                      <MUITableCell>{booking.service}</MUITableCell>
                      <MUITableCell>{booking.startTime ? formattedDate(booking.startTime) : '-'}</MUITableCell>
                      <MUITableCell>
                        {booking.startTime && booking.endTime ? getDuration(booking.startTime, booking.endTime) : '-'}
                      </MUITableCell>
                      <MUITableCell>
                        {booking.employeeId !== undefined ? (staffMap[booking.employeeId] ?? '-') : '-'}
                      </MUITableCell>
                      <MUITableCell>€{booking.pendingPrice}</MUITableCell>
                    </MUITableRow>
                  )
                })}
              </MUITableBody>
            </MUITable>
          </TableContainer> */}

          <DataTable data={filteredBookings} columns={columns} isLoading={isLoading} />
        </SalesTable>
      </MUIStack>

      <MUIMenu
        anchorEl={quickActions}
        open={Boolean(quickActions)}
        onClose={handleQuickClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <MUIMenuItem onClick={() => handleFilterSelect('All')} style={getMenuItemStyle('All')}>
          All
        </MUIMenuItem>
        <MUIMenuItem onClick={() => handleFilterSelect('No Show')} style={getMenuItemStyle('No Show')}>
          No Show
        </MUIMenuItem>
        <MUIMenuItem onClick={() => handleFilterSelect('Future Booking')} style={getMenuItemStyle('Future Booking')}>
          Future Booking
        </MUIMenuItem>
        <MUIMenuItem onClick={() => handleFilterSelect('Canceled')} style={getMenuItemStyle('Canceled')}>
          Canceled
        </MUIMenuItem>
        <MUIMenuItem onClick={() => {}}>Rescheduled</MUIMenuItem>
      </MUIMenu>
    </>
  )
}

export default Appointments
