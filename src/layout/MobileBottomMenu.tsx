import { FC, useState, useRef } from 'react'
import { useAuth0 } from '@auth0/auth0-react'
import { AppBar, Box, Fab, Toolbar, List, ListItem, ListItemText } from '@mui/material'
import { useLocation } from 'react-router-dom'
import { StyledMenu, StyledPopover, UtilityDropdown } from './Layout.style'
import NotificationDialog from './NotificationDialog'
import { NotificationBadgeComponent } from './Sidebar'
import MUIListItem from '../shared/components/MUIListItem'
import useStaffLoginDetailsSliceAction from '@/redux/slice/staffLoginSlice/useStaffLoginDetailsSliceAction'
import RouteLink from '@/shared/components/RouteLink'
import theme from '@/styles/theme'

const MobileBottomMenu: FC<{ onFabClick: () => void }> = ({ onFabClick }) => {
  const [isUtilityOpen, setIsUtilityOpen] = useState(false)
  const utilityRef = useRef<HTMLDivElement>(null)
  const location = useLocation()
  const [notification, setNotification] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { logout, isAuthenticated } = useAuth0()
  const { setStaffLoginDetails } = useStaffLoginDetailsSliceAction()
  const clientId = process.env.REACT_APP_CLIENT_ID || ''

  const utilityMenuItems = [
    { icon: 'ri-focus-fill', label: 'Marketing', to: '/marketing' },
    { icon: 'ri-line-chart-line', label: 'Reports', to: '/report' },
    { icon: 'ri-notification-fill', label: 'Notifications', onClick: () => setNotification(true), badge: true },
    { icon: 'ri-settings-2-fill', label: 'Settings', to: '/settings' },
    {
      icon: 'ri-logout-box-r-line',
      label: 'Logout',
      onClick: async () => {
        localStorage.clear()
        setIsLoading(true)
        try {
          await logout({
            logoutParams: { client_id: clientId },
          })
        } catch (error) {
          console.error('Logout failed:', error)
        } finally {
          setIsLoading(false)
        }
      },
    },
    { icon: 'ri-lock-password-fill', label: 'Pin Logout', onClick: () => setStaffLoginDetails(null) },
  ]

  // Determine if the utility icon should be active
  const isUtilityActive = utilityMenuItems.some(
    (item) => (item.to && location.pathname === item.to) || (item.label === 'Notifications' && notification),
  )

  const handleUtilityClick = () => {
    setIsUtilityOpen((prev) => !prev)
  }

  const handleUtilityClose = () => {
    setIsUtilityOpen(false)
  }

  return (
    <>
      <AppBar position="fixed" color="inherit" sx={{ top: 'auto', bottom: 0, zIndex: 1000 }}>
        <Toolbar sx={{ p: 0 }}>
          <StyledMenu>
            <Box
              ref={utilityRef}
              className={`remove-bottom-border ${isUtilityOpen || isUtilityActive ? 'active' : ''}`}
              onClick={handleUtilityClick}
              sx={{ cursor: 'pointer' }}
            >
              <MUIListItem>
                <i className="ri-apps-2-fill"></i>
              </MUIListItem>
            </Box>

            <RouteLink
              to="/catalog"
              className={`remove-bottom-border ${location.pathname === '/catalog' ? 'active' : ''}`}
            >
              <MUIListItem>
                <i className="ri-book-open-fill"></i>
              </MUIListItem>
            </RouteLink>

            <RouteLink to="/sales" className={`remove-bottom-border ${location.pathname === '/sales' ? 'active' : ''}`}>
              <MUIListItem>
                <i className="ri-price-tag-3-fill"></i>
              </MUIListItem>
            </RouteLink>

            <Fab
              onClick={onFabClick}
              sx={{
                backgroundColor: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: theme.palette.primary.dark,
                  boxShadow: 'none',
                },
              }}
            >
              <i className="ri-add-line"></i>
            </Fab>

            <RouteLink
              to="/team-members"
              className={`remove-bottom-border ${location.pathname === '/team-members' ? 'active' : ''}`}
            >
              <MUIListItem>
                <i className="ri-team-fill"></i>
              </MUIListItem>
            </RouteLink>

            <RouteLink
              to="/clients-list"
              className={`remove-bottom-border ${location.pathname === '/clients-list' ? 'active' : ''}`}
            >
              <MUIListItem>
                <i className="ri-user-fill"></i>
              </MUIListItem>
            </RouteLink>
            <RouteLink to="/" className={`remove-bottom-border ${location.pathname === '/' ? 'active' : ''}`}>
              <MUIListItem>
                <i className="ri-calendar-fill"></i>
              </MUIListItem>
            </RouteLink>
          </StyledMenu>
        </Toolbar>
      </AppBar>

      <StyledPopover
        open={isUtilityOpen}
        anchorEl={utilityRef.current}
        onClose={handleUtilityClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
      >
        <UtilityDropdown>
          <List>
            {utilityMenuItems.map((item, index) => (
              <ListItem
                key={index}
                sx={{ textAlign: 'left', padding: 0 }}
                className={item.to && location.pathname === item.to ? 'active' : ''}
              >
                {item.to ? (
                  <RouteLink
                    to={item.to}
                    className="remove-bottom-border"
                    onClick={handleUtilityClose}
                    style={{ display: 'flex', alignItems: 'center', justifyContent: 'start', textDecoration: 'none' }}
                  >
                    <MUIListItem sx={{ padding: 0 }}>
                      <i className={item.icon}></i>
                    </MUIListItem>
                    <ListItemText primary={item.label} />
                  </RouteLink>
                ) : (
                  <Box
                    onClick={() => {
                      item.onClick && item.onClick()
                      handleUtilityClose()
                    }}
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', justifyContent: 'start' }}
                  >
                    <MUIListItem sx={{ padding: 0 }}>
                      {item.badge ? (
                        <NotificationBadgeComponent icon={<i className="ri-notification-fill"></i>} />
                      ) : (
                        <i className={item.icon}></i>
                      )}
                    </MUIListItem>
                    <ListItemText primary={item.label} />
                  </Box>
                )}
              </ListItem>
            ))}
          </List>
        </UtilityDropdown>
      </StyledPopover>
      <NotificationDialog open={notification} onClose={() => setNotification(false)} />
    </>
  )
}

export default MobileBottomMenu
