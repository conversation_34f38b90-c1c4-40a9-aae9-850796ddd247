import { ReactNode } from 'react'
import { Paper, Popover, DialogProps } from '@mui/material'
import { Theme } from '@mui/material/styles' // Import Theme from Material-UI for proper typing
import { SxProps } from '@mui/system'
import MUIList from '../shared/components/MUIList'
import MUIStack from '../shared/components/MUIStack'
import { MUIStyled } from '../shared/components/MUIStyled'
import MUIBox from '@/shared/components/MUIBox'
import MUIDialog from '@/shared/components/MUIDialog'

// Typing for the theme parameter
export const BottomMenuList = MUIStyled(MUIList)(({ theme }: { theme: Theme }) => ({
  marginTop: 'auto',
  '& .MuiListItem-root': {
    padding: '0px',
    transition: 'all 0.2s ease-in-out',
  },
}))

export const DateSelection = MUIStyled(MUIStack)(({ theme }: { theme: Theme }) => ({
  border: `1px solid ${theme.palette.gray?.main}`,
  padding: '7px 10px',
  borderRadius: 20,
  '& .MuiFormControl-root': {
    '& .MuiInputBase-root': {
      padding: 0,
      '& .MuiInputBase-input': {
        padding: 0,
        fontSize: 14,
        textAlign: 'center',
        maxWidth: 100,
      },
    },
  },
  '& i': {
    color: `${theme.palette.black?.main}99`,
    cursor: 'pointer',
  },
}))

export const HeaderDateSelection = MUIStyled(MUIStack)(({ theme }: { theme: Theme }) => ({
  borderColor: `${theme.palette.primary.main}`,
  backgroundColor: `${theme.palette.primary.main}`,
  minWidth: 20,
  overflow: 'hidden',
  textAlign: 'center',
  mr: 2,
  height: 30,
  cursor: 'pointer',
  borderRadius: '8px 8px 0px 0px',
  '& .MuiFormControl-root': {
    position: 'absolute',
    top: 0,
    bottom: 0,
    '& .MuiInputBase-root': {
      padding: 0,
      height: '100%',
      '& .MuiInputBase-input': {
        padding: 0,
        fontWeight: 'bold',
        textAlign: 'center',
        maxWidth: 70,
        height: '100%',
        paddingBottom: 24,
        color: 'white',
      },
    },
  },
  '& i': {
    color: `${theme.palette.black?.main}99`,
    cursor: 'pointer',
  },
}))

export const FilterRight = MUIStyled(MUIStack)(({ theme }: { theme: Theme }) => ({
  '& .MuiIconButton-root': {
    width: 40,
    height: 40,
    border: `1px solid ${theme.palette.gray?.main}`,
    fontSize: 18,
  },
  '& .MuiButton-root': {
    '& i': {
      marginLeft: 4,
      fontSize: 18,
      lineHeight: 'normal',
      color: theme.palette.lightGray?.light200,
    },
    '&.MuiButton-containedPrimary': {
      '& i': {
        color: theme.palette.white?.main,
      },
    },
  },
}))

// Typing for the 'mainMenu' prop as boolean
export const SidebarWrapper = MUIStyled(MUIStack)<{ mainMenu: boolean }>( // Remove theme from prop types here/
  ({ theme, mainMenu }: { theme: Theme; mainMenu: boolean }) => ({
    position: 'fixed',
    left: 0,
    top: 0,
    bottom: 0,
    width: 200,
    zIndex: 99,
    transition: 'all 0.3s ease-in-out',
    [theme.breakpoints.down('sm')]: {
      left: mainMenu ? 0 : -250,
    },
    '& .MuiList-root': {
      '& .MuiListItem-root': {
        '& a': {
          textDecoration: 'none',
          gap: 8,
          color: `${theme.palette.black?.main}99`,
          display: 'flex',
          alignItems: 'center',
          borderRadius: 8,
          height: 36,
          width: '100%',
          transition: 'all 0.2s ease-in-out',
          '& i': {
            verticalAlign: 'middle',
            fontSize: 20,
            transition: 'all 0.2s ease-in-out',
          },
          '& span': {
            verticalAlign: 'middle',
            fontSize: 14,
            fontWeight: 600,
            color: theme.palette.secondary.main,
            transition: 'color 0.2s ease-in-out',
          },
        },
        '&.active': {
          backgroundColor: `${theme.palette.primary.main}14`,
          '& a': {
            '& i': {
              color: theme.palette.primary.main,
            },
            '& span': {
              color: theme.palette.primary.main,
            },
          },
        },
        '& .MuiBox-root': {
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
        },
      },
    },
  }),
)

interface NotificationDialogWrapperProps extends Omit<DialogProps, 'title'> {
  isExpanded: boolean
  title?: ReactNode
  dialogContentSX?: SxProps<Theme>
}

export const NotificationDialogWrapper = MUIStyled(MUIDialog, {
  shouldForwardProp: (prop) => prop !== 'isExpanded' && prop !== 'dialogContentSX',
})<NotificationDialogWrapperProps>(({ theme, isExpanded }) => ({
  // left: 170,
  '& .MuiDialog-container': {
    justifyContent: 'flex-start',
    '& .MuiDialogTitle-root': {
      borderBottom: `1px solid ${theme.palette.gray?.main}`,
    },
    '& .MuiPaper-root': {
      minHeight: 'calc(100vh - 60px)',
      margin: isExpanded ? theme.spacing(25) : theme.spacing(9),
      [theme.breakpoints.down('sm')]: {
        margin: 0,
      },
      zIndex: 1100,
      '& .MuiDialogTitle-root': {
        padding: '16px 20px',
        '& i': {
          marginRight: 8,
          fontSize: 18,
          verticalAlign: 'middle',
        },
      },
      '& .MuiDialogContent-root': {
        padding: 0,
        // borderTop: `1px solid ${theme.palette.gray?.main}`,
        '& .css-19id8hp': {
          padding: 0,
        },
      },
    },
  },
}))

export const NotificationList = MUIStyled(MUIList)(({ theme }: { theme: Theme }) => ({
  padding: 0,
  '& .MuiListItem-root': {
    borderBottom: `1px solid ${theme.palette.gray?.main}`,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
  },
}))

export const NotificationAvatar = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  maxWidth: 'max-content',
  position: 'relative',
  '& i': {
    position: 'absolute',
    right: -5,
    bottom: 0,
    width: 20,
    height: 20,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.white?.main,
    borderRadius: '100%',
    border: `1px solid ${theme.palette.white?.main}`,
    textAlign: 'center',
    lineHeight: '18px',
    fontSize: 10,
  },
}))
export const UserAvatar = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  '& .user-avatar': {
    width: 40,
    height: 40,
    fontSize: '13px',
    backgroundColor: '#DCD3FD',
    color: theme.palette.natural?.main,
    transition: 'all 0.2s ease-in-out',
  },
}))
export const UserAvatarLarge = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  '& .user-avatar': {
    width: 64,
    height: 64,
    fontSize: '20px',
    backgroundColor: '#DCD3FD',
    color: theme.palette.natural?.main,
  },
}))
export const UserAvatarDropDown = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  '& .user-avatar': {
    width: 20,
    height: 20,
    fontSize: '12px',
    backgroundColor: '#DCD3FD',
    color: theme.palette.natural?.main,
  },
}))

export const SidebarExpandDetailsWrapper = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  gap: 8,
  width: '100%',
  padding: '10px 24px',
  transition: 'all 0.2s ease-in-out',
  maxHeight: '50px',
  '&:hover': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& i': {
      color: theme.palette.primary.main,
    },
    '& p': {
      color: theme.palette.primary.main,
    },
  },
  '& i': {
    fontSize: 20,
    color: '#808080',
  },
  '& p': {
    fontSize: 16,
    fontWeight: 500,
    whiteSpace: 'nowrap',
    color: 'black',
  },
  '&.active': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& i': {
      color: theme.palette.primary.main,
    },
    '& p': {
      color: theme.palette.primary.main,
    },
  },
}))
export const SidebarExpandIconsWrapper = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  padding: '8px',
  borderRadius: '8px',
  transition: 'all 0.2s ease-in-out',
  maxHeight: '36px',
  marginBottom: 4,
  '&:hover': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& i': {
      color: theme.palette.primary.main,
    },
  },
  '& i': {
    fontSize: 20,
    color: '#808080',
  },
  '&.active': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& i': {
      color: theme.palette.primary.main,
    },
  },
}))
export const SidebarExpandRouteDetailsWrapper = MUIStyled(MUIBox)(({ theme }: { theme: Theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  gap: 8,
  width: '100%',
  transition: 'all 0.2s ease-in-out',
  maxHeight: '50px',
  '&:hover': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& i': {
      color: theme.palette.primary.main,
    },
    '& p': {
      color: theme.palette.primary?.main,
    },
  },
  '& i': {
    fontSize: 20,
    color: '#808080',
  },
  '& a': {
    padding: '10px 24px !important',
    height: '100% !important',
    borderRadius: '0px !important',
  },
  '& p': {
    fontSize: 16,
    fontWeight: 500,
    whiteSpace: 'nowrap',
    color: 'black',
  },
  '&.active': {
    backgroundColor: `${theme.palette.primary.main}14`,
    '& a': {
      '& i': {
        color: theme.palette.primary.main,
      },
      '& p': {
        color: theme.palette.primary.main,
      },
    },
  },
}))

export const StyledMenu = MUIStyled(MUIList)(({ theme }: { theme: Theme }) => ({
  display: 'flex',
  alignItems: 'center',
  flex: 1,
  justifyContent: 'space-between',
  '& .remove-bottom-border': {
    textDecoration: 'none !important',
  },
  '& .MuiListItem-root': {
    width: 'auto',
    '& i': {
      color: `${theme.palette.darkGray?.light}66`,
      fontSize: 20,
    },
  },
  '& .active': {
    '& i': {
      color: theme.palette.natural?.main,
    },
  },
  '& .MuiFab-root': {
    width: 40,
    height: 40,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.white?.main,
    fontSize: 20,
    boxShadow: `0px 1px 2px 0px ${theme.palette.gray?.light}0D`,
  },
}))

export const StyledPopover = MUIStyled(Popover)(({ theme }: { theme: Theme }) => ({
  '& .MuiPopover-paper': {
    backgroundColor: theme.palette.background?.paper || '#fff',
    boxShadow: `0px 4px 20px 0px ${theme.palette.gray?.light}33`,
    borderRadius: 12,
    minWidth: 150,
    marginBottom: 8,
  },
}))

export const UtilityDropdown = MUIStyled(Paper)(({ theme }: { theme: Theme }) => ({
  '& .MuiList-root': {
    padding: 8,
    textDecoration: 'none !important',
  },
  '& .MuiListItem-root': {
    borderRadius: 8,
    marginBottom: 4,
    width: 'auto',

    '&:hover': {
      backgroundColor: theme.palette.action?.hover || '#f5f5f5',
    },
    '&:last-child': {
      marginBottom: 0,
    },
    '& i': {
      color: `${theme.palette.darkGray?.light}66`,
      fontSize: 20,
    },
  },
  '& .MuiListItemText-root': {
    '& .MuiListItemText-primary': {
      fontSize: 16,
      fontWeight: 500,
      marginLeft: 8,
      color: `${theme.palette.darkGray?.light}66`,
    },
  },
  '& .active': {
    '& i': {
      color: theme.palette.natural?.main,
    },
    '& .MuiListItemText-primary': {
      color: theme.palette.natural?.main,
    },
  },
}))
