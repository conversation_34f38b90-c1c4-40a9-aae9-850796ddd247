import { useState } from 'react'
import { Box } from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Outlet, useLocation } from 'react-router-dom'
import MobileBottomMenu from './MobileBottomMenu'
import MobileHeader from './mobileHeader'
import Sidebar from './Sidebar'
import CustomScrollbar from '../shared/components/CustomScrollbar'
import MUIStack from '../shared/components/MUIStack'
import AddClientDetail from '@/features/appointments/components/addClientDetail'
import useCalendarSliceAction from '@/redux/slice/calenderSlice/useCalenderSliceAction'
import useGlobalSliceActions from '@/redux/slice/globalSlice/useGlobalSliceActions'
import { useSidebarSliceAction } from '@/redux/slice/sidebarSlice/useSidebarSliceAction'

const Layout = () => {
  const matches = useMediaQuery('(max-width:767px)')
  const { pathname } = useLocation()
  const { selectedSlot, onMobileFabAdd, onMobileFabAddClose, isWeekView } = useCalendarSliceAction()
  const { selectedWeekDate, selectedAppointmentDate } = useGlobalSliceActions()
  const [isNewAppointment, setIsNewAppointment] = useState(false)
  const { isExpanded } = useSidebarSliceAction()

  return (
    <>
      <MUIStack>
        {matches && (pathname === '/appointments' || pathname === '/') && !selectedSlot && <MobileHeader />}
        <Sidebar />
        <Box
          sx={{
            ml: { sm: isExpanded ? 23 : 8 },
            height: { sm: '100vh', xs: 'calc(100vh - 60px)' },
            transition: 'margin-left 0.3s ease, padding-left 0.3s',
            p: { sm: 2.25 },
            pl: { sm: isExpanded ? 2.25 : 0 },
          }}
        >
          <CustomScrollbar>
            <Outlet />
          </CustomScrollbar>
        </Box>
        {matches && (
          <MobileBottomMenu
            onFabClick={() => {
              onMobileFabAdd(isWeekView ? selectedWeekDate : selectedAppointmentDate)
              setIsNewAppointment(true)
            }}
          />
        )}
      </MUIStack>

      {isNewAppointment && (
        <AddClientDetail
          open={isNewAppointment}
          onClose={() => {
            onMobileFabAddClose()
            setIsNewAppointment(false)
          }}
          isAdd={true}
        />
      )}
    </>
  )
}

export default Layout
