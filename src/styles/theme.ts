import { createTheme, PaletteColorOptions } from '@mui/material/styles'

// Extend MUI's PaletteColor interface to include custom properties
declare module '@mui/material/styles' {
  interface PaletteColor {
    light50?: string
    light100?: string
    light200?: string
    light300?: string
  }
  interface SimplePaletteColorOptions {
    light50?: string
    light100?: string
    light200?: string
    light300?: string
  }

  interface Palette {
    gray?: PaletteColor
    lightGray?: PaletteColor
    darkGray?: PaletteColor
    black?: PaletteColor
    white?: PaletteColor
    natural?: PaletteColor
    blue?: PaletteColor
    orange?: PaletteColor
    danger?: PaletteColor
  }
  interface PaletteOptions {
    gray?: PaletteColorOptions
    lightGray?: PaletteColorOptions
    darkGray?: PaletteColorOptions
    black?: PaletteColorOptions
    white?: PaletteColorOptions
    natural?: PaletteColorOptions
    blue?: PaletteColorOptions
    orange?: PaletteColorOptions
    danger?: PaletteColorOptions
  }
}

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xxs: true // Add `xxs`
    xsm: true // Add `xsm`
    xs: true // Keep default `xs`
    sm: true // Keep default `sm`
    md: true // Keep default `md`
    lg: true // Keep default `lg`
    xl: true // Keep default `xl`
  }
}

const theme = createTheme({
  palette: {
    background: {
      default: '#F7F7F8',
    },
    primary: {
      main: '#320A57',
      light: '#ECDFFB',
      dark: '#5314A3',
      light100: '#C8B2FF',
      light200: '#B78AF0',
    },
    secondary: {
      main: '#576F89',
    },
    gray: {
      main: '#DEE0E3',
      light: '#14151A',
      dark: '#BABDC5',
    },
    lightGray: {
      main: '#C8CAD0',
      light: '#0A0F29',
      light50: '#F5F5F5',
      light100: '#0A0F2914',
      light200: '#0F132499',
      light300: '#717684',
    },
    darkGray: {
      main: '#E9EAEC',
      light: '#0D1126',
    },
    black: {
      main: '#0F1324',
      light: '#0F1324',
      dark: '#0B0C0E',
    },
    white: {
      main: '#FFFFFF',
    },
    natural: {
      main: '#924FE8',
      light: '#F7F1FD',
      contrastText: '#ffffff',
    },
    success: {
      main: '#26BD6C',
      light: '#D1FAE4',
      dark: '#166E3F',
      contrastText: '#ffffff',
    },
    blue: {
      main: '#4778F5',
    },
    orange: {
      main: '#F48E2F',
      contrastText: '#ffffff',
    },
    danger: {
      main: '#E6483D',
      contrastText: '#FFFFFF',
      light: '#FCE5E4',
      dark: '#9A1C13',
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      xxs: 475,
      xsm: 600,
      sm: 768,
      md: 992,
      lg: 1200,
      xl: 1536,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'capitalize',
          minHeight: 40,
        },
      },
    },
  },
  typography: {
    fontFamily: '"Inter", sans-serif',
    body1: {
      color: '#14151A',
    },
  },
})

export default theme
