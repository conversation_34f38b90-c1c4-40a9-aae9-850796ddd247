import { Auth0Provider } from '@auth0/auth0-react'
import { ThemeProvider } from '@emotion/react'
import './App.scss'
import { CssBaseline } from '@mui/material'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { Toaster } from 'sonner'
import { LoginProvider } from './context/loginContext'
import { NotificationProvider } from './context/notificationContext'
import { store } from './redux/store'
import AllRoutes from './routes/index'
import InactivityTracker from './shared/components/inactivityModel/InactivityTracker'
import { CalendarProvider } from '@/features/appointments/components/calender/context'
import theme from '@/styles/theme'
import 'remixicon/fonts/remixicon.css'

function App() {
  const googleDomain = process.env.REACT_APP_GOOGLE_DOMAIN || ''
  const clientId = process.env.REACT_APP_CLIENT_ID || ''

  return (
    <div className="App">
      <Auth0Provider
        domain={googleDomain}
        cacheLocation="localstorage"
        useRefreshTokens={true}
        clientId={clientId}
        authorizationParams={{
          redirect_uri: typeof window !== 'undefined' ? window.location.origin : undefined,
        }}
        onRedirectCallback={(appState: any) => {
          // Move `useNavigate` logic inside `AppWrapper`
          window.location.href = appState?.returnTo || '/appointments'
        }}
      >
        <DndProvider backend={HTML5Backend}>
          <ThemeProvider theme={theme}>
            <BrowserRouter>
              <Toaster position="top-right" richColors />
              <LoginProvider>
                <Provider store={store}>
                  <NotificationProvider>
                    <InactivityTracker>
                      <CssBaseline />
                      <AllRoutes />
                    </InactivityTracker>
                  </NotificationProvider>
                </Provider>
              </LoginProvider>
            </BrowserRouter>
          </ThemeProvider>
        </DndProvider>
      </Auth0Provider>
    </div>
  )
}

export default App
