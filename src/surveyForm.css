/* change title color */
.sd-title {
  color: #320a57 !important;
  padding: 0px 0px 0px 0px !important;
}
.sd-title h3{
  padding: 0px 0px 24px 0px !important;
}

/* word should be in form area */
.sv-string-viewer {
  word-wrap: break-word;
}

/* form body */
.sd-body.sd-body--static {
  padding-bottom: 0px !important;
  padding-top: 0px !important;
}

.sd-body {
  background-color: #ffffff;
  padding-top: 0px;
}
.sd-page{
  padding: 0px !important;
}
.sd-element--with-frame {
  padding: 16px 32px 32px !important;
  box-shadow: none !important;
  border-radius: 12px;
  border: 1px solid #00000020 !important;
}

.sd-page__empty-header {
  padding: 0px !important;
}
.sd-page__empty-header .sd-element--with-frame {
  box-shadow: none;
  border-radius: 12px;
  border: 1px solid #00000020;
}

.sd-action-bar {
  padding-left: 0px !important;
}

/* form button */
.sv-action__content input {
  padding: 6px 64px !important;
  border-radius: 8px;
  background-color: #320a57;
  height: 40px;
  color: #ffffff;
}

.sv-action__content input:hover {
  background-color: #5314a3;
}

/* input field */
.sd-text__content input {
  background-color: #ffffff;
  box-shadow: none;
  border: 1px solid #00000020;
  border-radius: 12px;
  max-height: 42px;
}
.sd-text__content input:focus {
  box-shadow: none;
}
.sd-text__content input:hover {
  border: 1px solid #c8b2ff;
}

/* dropdown */
.sd-dropdown {
  border-radius: 12px !important;
  box-shadow: none !important;
  background-color: #ffffff !important;
  border: 1px solid #00000020 !important;
}

.sd-input.sd-dropdown:focus-within,
.sd-item__control:focus + .sd-item__decorator {
  box-shadow:
    var(--sjs-shadow-inner-reset, inset 0px 0px 0px 0px rgba(0, 0, 0, 0.15)),
    0 0 0 2px #c8b2ff,
    0 0 5px 2px rgba(200, 178, 255, 0.5) !important;
}
.sv-list__item.sv-list__item--selected > .sv-list__item-body,
.sv-list__item.sv-list__item--selected:hover > .sv-list__item-body,
.sv-list__item.sv-list__item--selected.sv-list__item--focused > .sv-list__item-body,
.sv-multi-select-list .sv-list__item.sv-list__item--selected.sv-list__item--focused > .sv-list__item-body,
li:focus .sv-list__item.sv-list__item--selected > .sv-list__item-body {
  background-color: #320a57 !important;
  color: var(--sjs-primary-forecolor, var(--primary-foreground, #fff));
  font-weight: 600;
}

/* checkbox */
.sd-checkbox__decorator {
  border: 1px solid #320a57 !important;
}

.sd-item--checked .sd-item__decorator {
  background: #320a57 !important;
  outline: #320a57 !important;
}
.sd-item__decorator {
  border: 1px solid #320a57 !important;
}
.sd-checkbox--checked .sd-checkbox__control:focus + .sd-checkbox__decorator .sd-checkbox__svg use {
  fill: #ffffff !important;
}

/* radio */
.sd-radio--checked .sd-radio__control:focus + .sd-radio__decorator:after {
  background-color: #ffffff !important;
}
label span::after {
  background-color: #320a57 !important;
}

/* prev & next button */
.sd-navigation__prev-btn,
.sd-navigation__next-btn {
  background-color: transparent !important;
  color: #320a57 !important;
  border: 1px solid #320a57 !important;
  box-shadow: none;
}
