import { useEffect } from 'react'
import { useRoutes, useNavigate } from 'react-router-dom'
import { privetRoute } from './route.description'
import useStaffLoginDetailsSliceAction from '@/redux/slice/staffLoginSlice/useStaffLoginDetailsSliceAction'
import { useGetPermissions } from '@/shared/hooks/apis/permissions/getPermissions'

const AllRoutes = () => {
  const { staffLoginDetails } = useStaffLoginDetailsSliceAction()
  const { data: permissions } = useGetPermissions()
  const navigate = useNavigate()

  useEffect(() => {
    // Check if user is not logged in (role is null/undefined)
    if (!staffLoginDetails?.role) {
      navigate('/staff-login', { replace: true })
      return
    }
  }, [staffLoginDetails?.role, navigate])

  // If no role, return null while navigation happens
  if (!staffLoginDetails?.role) {
    return null
  }

  const routes = privetRoute(Number(staffLoginDetails.role), permissions)
  const allRoutes = useRoutes(routes)

  return allRoutes
}

export default AllRoutes
