import React from 'react'
import { Navigate } from 'react-router-dom'
import { PermissionsResponse, ROLE_MAPPING, ROUTE_PERMISSION_MAPPING, UserRole } from '@/shared/helper'

interface ProtectedRouteProps {
  children: React.ReactElement
  requiredPermission?: keyof typeof ROUTE_PERMISSION_MAPPING
  userRole: UserRole
  permissions: PermissionsResponse | null
  fallbackPath?: string
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  userRole,
  permissions,
  fallbackPath = '/unauthorized',
}) => {
  // If no permissions data yet, show loading or allow access temporarily
  if (!permissions) {
    return children // or return a loading spinner
  }

  // Get the role name from the mapping
  const roleName = ROLE_MAPPING[userRole]

  if (!roleName) {
    return <Navigate to={fallbackPath} replace />
  }

  // Get user's permissions for their role
  const userPermissions: any = permissions[roleName]?.permissions?.pages

  if (!userPermissions) {
    return <Navigate to={fallbackPath} replace />
  }

  // If no specific permission required, allow access
  if (!requiredPermission) {
    return children
  }

  // Check if user has the required permission
  const hasPermission = userPermissions[requiredPermission]

  if (!hasPermission) {
    return <Navigate to={fallbackPath} replace />
  }

  return children
}

export default ProtectedRoute
