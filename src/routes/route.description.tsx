import { lazy } from 'react'
import { Navigate, RouteObject } from 'react-router-dom'
import ProtectedRoute from './ProtectedRoute'
import Layout from '@/layout/Layout'
import AuthGuard from '@/shared/components/authGuard/components/AuthGuard'

const ClientsList = lazy(() => import('@/features/clientsList/pages/clientList/ClientsList'))
const Sales = lazy(() => import('@/features/sales/pages/Sales'))
const ClientProfile = lazy(() => import('@/features/clientProfile/pages/clientProfile/ClientProfile'))
const Catalog = lazy(() => import('@/features/catalog/pages/catalog/Catalog'))
const TeamMembers = lazy(() => import('@/features/teamMembers/pages/TeamMembers/TeamMembers'))
const StaffAccess = lazy(() => import('@/features/teamMembers/pages/StaffAccess'))
const AssignedServices = lazy(() => import('@/features/teamMembers/pages/assignedServices'))
const PerformanceDashboard = lazy(() => import('@/features/teamMembers/pages/PerformanceDashboard'))
const Report = lazy(() => import('@/features/report/pages/Report'))
const AppointmentsPage = lazy(() => import('@/features/appointments/pages/appointments/index'))
const Rota = lazy(() => import('@/features/teamMembers/components/Rota'))
const UnauthorizedPage = lazy(() => import('@/features/teamMembers/pages/unauthorized'))
const StaffLoginPage = lazy(() => import('@/features/teamMembers/pages/staffLogin'))
const Settings = lazy(() => import('@/features/settings/pages/settings/Settings'))

export const privetRoute = (userRole: number, permissions: any): RouteObject[] => {
  return [
    {
      element: <Layout />,
      path: '/',
      children: [
        // {
        //   element: <Fusion />,
        //   path: '/fusion',
        // },
        {
          path: '/',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="appointments">
              <AppointmentsPage />
            </ProtectedRoute>
          ),
          // <AuthGuard requiredPermission={PERMISSION.APPOINTMENTS} element={<AppointmentsPage />} />,
        },
        // {
        //   path: '/appointments',
        //   element: (
        //     <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="appointments">
        //       <AppointmentsPage />
        //     </ProtectedRoute>
        //   ),
        // },
        {
          path: '/sales',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="make_a_sale">
              <Sales />
            </ProtectedRoute>
          ),
        },
        {
          path: '/clients-list',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="clients">
              <ClientsList />
            </ProtectedRoute>
          ),
        },
        {
          path: '/client-profile/:slug',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="clients">
              <ClientProfile />
            </ProtectedRoute>
          ),
        },
        {
          path: '/catalog',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="sales_manager">
              <Catalog />
            </ProtectedRoute>
          ),
        },
        {
          path: '/team-members',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="staff">
              <TeamMembers />
            </ProtectedRoute>
          ),
        },
        {
          path: '/rota',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="staff">
              <Rota />
            </ProtectedRoute>
          ),
        },
        {
          path: '/staff-access',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="staff">
              <StaffAccess />
            </ProtectedRoute>
          ),
        },
        {
          path: '/assigned-services',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="staff_services">
              <AssignedServices />
            </ProtectedRoute>
          ),
        },
        {
          path: '/performance-dashboard',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="staff">
              <PerformanceDashboard />
            </ProtectedRoute>
          ),
        },
        {
          path: '/report',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="reports">
              <Report />
            </ProtectedRoute>
          ),
        },
        {
          path: '/settings',
          element: (
            <ProtectedRoute userRole={userRole} permissions={permissions} requiredPermission="settings">
              <Settings />
            </ProtectedRoute>
          ),
        },
        {
          path: '/unauthorized',
          element: <UnauthorizedPage />,
        },
        {
          path: '/staff-login',
          element: <StaffLoginPage />,
        },
        //* Uncomment the following lines if you want to add the Reset Pin page
        // {
        //   path: '/reset-pin',
        //   element: <ResetPinPage />,
        // },
      ],
    },
  ]
}
