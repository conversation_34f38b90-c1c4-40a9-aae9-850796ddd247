import React, { ReactElement, MouseEvent, cloneElement, isValidElement } from 'react'
import { toast } from 'sonner'
import { TAB_PERMISSION_MAPPING, usePermissions } from '../shared/helper'

interface RestrictedRouteProps {
  permissionKey: keyof typeof TAB_PERMISSION_MAPPING
  index: number
  onRequestTabChange: (index: number) => void
  children: ReactElement<any>
}

const RestrictedRoute = ({ permissionKey, index, onRequestTabChange, children }: RestrictedRouteProps) => {
  const { checkPermission } = usePermissions()

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    const requiredPermission = TAB_PERMISSION_MAPPING[permissionKey]
    if (!requiredPermission) {
      return
    }

    if (!checkPermission(requiredPermission)) {
      toast.error('You do not have permission to access this tab.')
      event.preventDefault()
      return
    }

    onRequestTabChange(index)

    // Safe access to onClick with type checking
    const originalOnClick = (children.props as any)?.onClick
    if (typeof originalOnClick === 'function') {
      originalOnClick(event)
    }
  }

  // Validate the child is a valid element
  if (!isValidElement(children)) {
    return null
  }

  return cloneElement(children, {
    ...(children.props as any),
    onClick: handleClick,
  })
}

export default RestrictedRoute
